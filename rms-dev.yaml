apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  namespace: dev
  labels:
    app: rms
  name: rms
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rms
  strategy: {}
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: rms
    spec:
      containers:
        - image: 10.1.2.150:5000/rms:dev
          name: rms
          resources:
            requests:
              memory: "1024Mi"
              cpu: "128m"
            limits:
              memory: "1024Mi"
          imagePullPolicy: Always
          # livenessProbe:
          #   httpGet:
          #     path: /actuator/health
          #     port: 8080
          #   initialDelaySeconds: 20
          #   periodSeconds: 10
          #   timeoutSeconds: 5
          #   failureThreshold: 3
          #   successThreshold: 1
          # readinessProbe:
          #   httpGet:
          #     path: /actuator/health
          #     port: 8080
          #   initialDelaySeconds: 20
          #   periodSeconds: 10
          #   timeoutSeconds: 5
          #   failureThreshold: 3
          #   successThreshold: 1
          # lifecycle:
          #   preStop:
          #     exec:
          #       command: ["/bin/sh", "-c", "sleep 10"]
          env:
            - name: SPRING_DATASOURCE_URL
              value: "************************************"
          #   - name: SPRING_DATASOURCE_mersNAME
          #     value: "ihisblank"
          #   - name: SPRING_DATASOURCE_PASSWORD
          #     value: "ihisblank"
          #   - name: SPRING_DATASOURCE_DRIVER_CLASS_NAME
          #     value: "org.postgresql.Driver"
          #   - name: SPRING_JPA_HIBERNATE_DDL_AUTO
          #     value: "none"
          #   - name: SERVER_PORT
          #     value: "8080"
          #   - name: KEYCLOAK_SERVER_URL
          #     value: "http://keycloak:8080"
          #   - name: KEYCLOAK_REALM
          #     value: "phis-cloud"
          #   - name: KEYCLOAK_CLIENT_ID
          #     value: "admin-cli"
          #   - name: KEYCLOAK_CLIENT_SECRET
          #     value: "DOeVM4tp1NOjSAUhKL5h0QK13eOIVzGq"
          #   - name: PHISCLOUD_MAILER_URL
          #     value: "http://mailer:9003/api/v1/mailer"
          #   - name: PHISCLOUD_GENERATOR_URL
          #     value: "http://generator:8080/api/v1/generator/phis"
      terminationGracePeriodSeconds: 50

status: {}
---
apiVersion: v1
kind: Service
metadata:
  creationTimestamp: null
  namespace: dev
  labels:
    app: rms
  name: rms
spec:
  ports:
    - name: 8080-8080
      port: 8080
      protocol: TCP
      targetPort: 8080
      nodePort: 30280
  selector:
    app: rms
  type: NodePort
# ---
# apiVersion: autoscaling/v2
# kind: HorizontalPodAutoscaler
# metadata:
#   creationTimestamp: null
#   name: mers
# spec:
#   maxReplicas: 1
#   minReplicas: 1
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name: mers
#   metrics:
#     - type: Resource
#       resource:
#         name: memory
#         target:
#           type: Utilization
#           averageUtilization: 90
#     - type: Resource
#       resource:
#         name: cpu
#         target:
#           type: Utilization
#           averageUtilization: 70
