package my.com.cmg.rms.repository.jpa;

import java.util.List;
import my.com.cmg.rms.model.SecUserRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface SecUserRoleRepository extends JpaRepository<SecUserRole, Long> {

  @Query("SELECT ur FROM SecUserRole ur WHERE ur.usrId = :userId AND ur.activeFlag = 'A'")
  List<SecUserRole> findByUserId(@Param("userId") Long userId);

  @Query(
      "SELECT ur FROM SecUserRole ur WHERE ur.usrId = :userId AND ur.divisionId = :divisionId AND"
          + " ur.activeFlag = 'A'")
  List<SecUserRole> findByUserIdAndDivision(
      @Param("userId") Long userId, @Param("divisionId") Long divisionId);
}