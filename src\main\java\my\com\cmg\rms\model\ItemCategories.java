package my.com.cmg.rms.model;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_item_categories")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ItemCategories extends BaseEntity {

  @Id
  @Column(name = "itm_cat_seqno", unique = true, nullable = false)
  @SequenceGenerator(name = "item_category_seq_no", sequenceName = "rm_item_categories_seq", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "item_category_seq_no")
  private Long itmCatSeqno;

  @Column(name = "itm_cat_code", length = 6, nullable = false)
  private String itmCatCode;

  @Column(name = "cat_desc", length = 50)
  private String catDesc;

  @Column(name = "order_t_code", length = 2)
  private String orderTCode;

  @Column(name = "categr_sts", length = 1)
  private String categrSts;

  @Column(name = "iwp_sync_date")
  private LocalDateTime iwpSyncDate;
}