package my.com.cmg.rms.model;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@MappedSuperclass
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseEntity {

  @Column(name = "parameter1", length = 100)
  private String parameter1;

  @Column(name = "parameter2", length = 100)
  private String parameter2;

  @Column(name = "parameter3", precision = 8, scale = 4)
  private BigDecimal parameter3;

  @Column(name = "parameter4", precision = 8, scale = 4)
  private BigDecimal parameter4;

  @Column(name = "parameter5", length = 13)
  private LocalDate parameter5;

  @Column(name = "active_flag")
  private Character activeFlag;

  @Column(name = "created_by", nullable = false)
  private Long createdBy;

  @Column(name = "created_date", nullable = false, length = 29)
  private LocalDateTime createdDate;

  @Column(name = "updated_by", nullable = false)
  private Long updatedBy;

  @Column(name = "updated_date", nullable = false, length = 29)
  private LocalDateTime updatedDate;
}
