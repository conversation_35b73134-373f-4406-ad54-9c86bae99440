package my.com.cmg.rms.service.impl;

import java.util.List;

import lombok.AllArgsConstructor;
import my.com.cmg.rms.constant.RmsConstant;
import my.com.cmg.rms.dto.RefCodesDTO;
import my.com.cmg.rms.repository.jooq.UtilsRepositoryJooq;
import my.com.cmg.rms.service.IUtilsService;

import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class UtilsService implements IUtilsService {
  private final UtilsRepositoryJooq utilsRepoJooq;

  public List<RefCodesDTO> getRequestType() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.DATA_REQUEST_TYPE);
    return response;
  }

  public List<RefCodesDTO> getCategory() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.DATA_REQUEST_CATEGORY);
    return response;
  }

  public List<RefCodesDTO> getAssignment() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.DATA_REQUEST_ASSIGNMENT);
    return response;
  }

  public List<RefCodesDTO> getStatus() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.DATA_REQUEST_STATUS);
    return response;
  }

  @Override
  public List<RefCodesDTO> getSubCategory(String category) {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomainAndParent(RmsConstant.DATA_REQUEST_SUB_CATEGORY_NEW,
        category);
    return response;
  }

  public List<RefCodesDTO> getIntention() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.DATA_REQUEST_INTENTION);
    return response;
  }

  @Override
  public List<RefCodesDTO> getAllDosage() {
    List<RefCodesDTO> response =  utilsRepoJooq.getAllDosage();
    return response;
  }

  @Override
  public List<RefCodesDTO> getItemClass() {
    List<RefCodesDTO> response = utilsRepoJooq.getItemClass();
    return response;
  }

  @Override
  public List<RefCodesDTO> getItemSubClass() {
    List<RefCodesDTO> response = utilsRepoJooq.getItemSubClass();
    return response;
  }

  @Override
  public List<RefCodesDTO> getState() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.STATE_MY);
    return response;
  }

  @Override
  public List<RefCodesDTO> getItemGroup() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.ITEM_GROUP);
    return response;
  }

  @Override
  public List<RefCodesDTO> getDrugSchedule() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.DRUG_SCHEDULE);
    return response;
  }

  @Override
  public List<RefCodesDTO> getDrugType() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.DRUG_TYPE);
    return response;
  }

  @Override
  public List<RefCodesDTO> getSpecialOrderConfiguration() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.RP_ITEM_TYPE);
    response.addAll(utilsRepoJooq.getRefCodesByDomain(RmsConstant.PN_TYPE));
    response.addAll(utilsRepoJooq.getRefCodesByDomain(RmsConstant.IV_ITEM_TYPE));
    return response;
  }

  @Override
  public List<RefCodesDTO> getSku() {
    List<RefCodesDTO> response = utilsRepoJooq.getSku();
    return response;
  }

  @Override
  public List<RefCodesDTO> getPku() {
    List<RefCodesDTO> response = utilsRepoJooq.getPku();
    return response;
  }

  @Override
  public List<RefCodesDTO> getRadiopharmaceuticalItem() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.RP_ITEM_TYPE);
    return response;
  }

  @Override
  public List<RefCodesDTO> getFacilityGroup() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.FACILITY_GROUP);
    return response;
  }

  @Override
  public List<RefCodesDTO> getMinistry() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.FACILITY_MINISTRY);
    return response;
  }

  @Override
  public List<RefCodesDTO> getFacilityCategory() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.FACILITY_CATEGORY);
    return response;
  }

  @Override
  public List<RefCodesDTO> getFacilityType() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.FACILITY_TYPE);
    return response;
  }

  @Override
  public List<RefCodesDTO> getCompanyStatus() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.COMPANY_STATUS);
    return response;
  }

  @Override
  public List<RefCodesDTO> getItemDetail() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.DATA_ITEM_DETAILS);
    return response;
  }

  @Override
  public List<RefCodesDTO> getDivision() {
    List<RefCodesDTO> response = utilsRepoJooq.getRefCodesByDomain(RmsConstant.MOH_DIVISION);
    return response;
  }
}
