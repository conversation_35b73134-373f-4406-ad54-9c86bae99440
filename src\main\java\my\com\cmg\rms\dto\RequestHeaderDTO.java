package my.com.cmg.rms.dto;

public record RequestHeaderDTO(
    Long requestHdrSeqno,
    String requestNo,
    String requestType,
    String requestTypeDesc,
    String category,
    String categoryDesc,
    String subCategory,
    String subCategoryDesc,
    String title,
    String reference,
    String intention,
    String intentionDesc,
    String reason,
    Long facilitySeqno,
    String facilityName,
    Long requestedBySeqno,
    String requestedByName,
    String requestedDate,
    String assignedTo,
    String assignedToDesc,
    String assignedFrom,
    String assignedFromDesc,
    String status,
    String statusDesc,
    String rejectReason,
    Long createdDate,
    Long updatedDate,
    String activeFlag,
    Long createdBy,
    Long updatedBy,
    RequestDtlDTO[] requestDetails) {}
