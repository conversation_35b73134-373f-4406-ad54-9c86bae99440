package my.com.cmg.rms.utils;

import java.util.Optional;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.dto.paging.PaginationResponseDTO;
import my.com.cmg.rms.mapper.ColumnMapper;

/**
 * @see DateUtil for date related utilities
 * @see TableUtil for table related utilities
 * @see PaginationUtil for pagination related utilities
 * @see LogUtil for loggers related utilities
 * @see CommonUtil for common related utilities
 */
public class PaginationUtil {
  /**
   * @param pageRq required: PaginationRequestDTO
   * @param mapper required: mapperClass must extends ColumnMapper
   * @return PaginationRequestDTO: validated sort, sortDirection, page, and size
   */
  public static PaginationRequestDTO pageSorting(
      PaginationRequestDTO pageRq, ColumnMapper mapper, Boolean multiDefaultSort) {

    String sort = mapper.getColumnName(pageRq.sort());

    if (multiDefaultSort) {
      sort = mapper.getMultipleColumnName(pageRq.sort());
    }

    Long page = pageRq.page() != null ? pageRq.page() : 1L;
    Long size = pageRq.size() != null ? pageRq.size() : 10L;

    String sortDirection = "asc";
    if (pageRq.sortDirection() != null && pageRq.sortDirection().contains("desc")) {
      sortDirection = "desc";
    }
    return new PaginationRequestDTO(sort, sortDirection, page, size);
  }

  // evaluate pageRq and put default pagination if not exist with default sorting
  public static PaginationRequestDTO pageSorting(
      PaginationRequestDTO pageRq,
      ColumnMapper mapper,
      String defaultSortDirection,
      Boolean multiDefaultSort) {

    String sort = mapper.getColumnName(pageRq.sort());

    if (multiDefaultSort) {
      sort = mapper.getMultipleColumnName(pageRq.sort());
    }

    Long page = pageRq.page() != null ? pageRq.page() : 1L;
    Long size = pageRq.size() != null ? pageRq.size() : 10L;

    String sortDirection =
        Optional.ofNullable(pageRq.sortDirection())
                .orElse(Optional.ofNullable(defaultSortDirection).orElse("asc"))
                .contains("desc")
            ? "desc"
            : "asc";

    return new PaginationRequestDTO(sort, sortDirection, page, size);
  }

  /** Retrieves pagination without sorting, only page and size */
  public static PaginationRequestDTO pageSorting(PaginationRequestDTO pageRq) {

    Long page = pageRq.page() != null ? pageRq.page() : 1L;
    Long size = pageRq.size() != null ? pageRq.size() : 10L;

    return new PaginationRequestDTO(null, null, page, size);
  }

  /** Retrieves PaginationResponseDTO from size and total */
  public static PaginationResponseDTO pagination(Long reqSize, Long total) {
    Long size = reqSize != null ? reqSize : 10L;
    Long totalPages = total / size;
    if (total % size > 0) {
      totalPages++;
    }
    return new PaginationResponseDTO(totalPages, total, size);
  }
}
