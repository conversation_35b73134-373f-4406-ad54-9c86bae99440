package my.com.cmg.rms.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rms_sec_userrole")
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class SecUserRole extends BaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "usr_rol_id")
  private Long usrRolId;

  @Column(name = "usr_id")
  private Long usrId;

  @Column(name = "rol_id")
  private Long rolId;

  @Column(name = "division_id")
  private Long divisionId;

  @Version
  @Column(name = "version", columnDefinition = "integer default 0")
  private Integer version;

  @Column(name = "iwp_sync_date")
  private LocalDateTime iwpSyncDate;
}