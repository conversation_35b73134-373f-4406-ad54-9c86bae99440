package my.com.cmg.rms.security;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;

public class SecurityUtil {

  public static String getCurrentUsername() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication != null && authentication.getPrincipal() instanceof Jwt) {
      Jwt jwt = (Jwt) authentication.getPrincipal();
      return jwt.getClaimAsString("preferred_username");
    }
    return null;
  }

  public static Long getCurrentUserId() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication != null && authentication.getPrincipal() instanceof Jwt) {
      Jwt jwt = (Jwt) authentication.getPrincipal();
      String userIdStr = jwt.getClaimAsString("user_id");
      if (userIdStr != null) {
        try {
          return Long.parseLong(userIdStr);
        } catch (NumberFormatException e) {
          return null;
        }
      }
    }
    return null;
  }

  public static boolean hasRole(String role) {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    return authentication.getAuthorities().stream()
        .anyMatch(authority -> authority.getAuthority().equals("ROLE_" + role));
  }

  public static boolean isAdmin() {
    return hasRole("ADMIN") || hasRole("SYSTEM_ADMIN");
  }
}