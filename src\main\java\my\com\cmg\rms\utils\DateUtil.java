package my.com.cmg.rms.utils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import my.com.cmg.rms.exception.ExceptionCode;
import my.com.cmg.rms.exception.RmsException;

/**
 * @see DateUtil for date related utilities
 * @see TableUtil for table related utilities
 * @see PaginationUtil for pagination related utilities
 * @see LogUtil for loggers related utilities
 * @see CommonUtil for common related utilities
 */
public class DateUtil {

  public static final SimpleDateFormat DD_MM_YYWITHSLASH = new SimpleDateFormat("dd/MM/yyyy");
  public static DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("hh:mm a");
  public static final String DATE_FORMAT_STR_1 = "dd/MM/yyyy";
  public static final String DATE_FORMAT_STR_2 = "yyyy-MM-dd";
  public static final String DATE_FORMAT_STR_3 = "yyyy-MM-dd HH:mm:ss";
  public static final String DATE_FORMAT_STR_4 = "MM/dd/yyyy";
  public static final String DATE_FORMAT_STR_5 = "dd/MM/yyyy hh:mm a";
  public static final String DATE_FORMAT_STR_6 = "h:mm a";
  public static final String DATE_FORMAT_STR_7 = "dd-MM-yyyy";
  public static final String DATE_FORMAT_STR_8 = "HH:mm";
  public static final String DATE_FORMAT_STR_9 = "yyyy-MM-dd HH:mm:ss a";
  public static final String DATE_FORMAT_STR_10 = "dd/MM/yyyy HH:mm";

  public static final DateTimeFormatter DATE_FORMAT_1 =
      DateTimeFormatter.ofPattern(DATE_FORMAT_STR_1);
  public static final DateTimeFormatter DATE_FORMAT_2 =
      DateTimeFormatter.ofPattern(DATE_FORMAT_STR_2);
  public static final DateTimeFormatter DATE_FORMAT_3 =
      DateTimeFormatter.ofPattern(DATE_FORMAT_STR_3);
  public static final DateTimeFormatter DATE_FORMAT_4 =
      DateTimeFormatter.ofPattern(DATE_FORMAT_STR_4);
  public static final DateTimeFormatter DATE_FORMAT_5 =
      DateTimeFormatter.ofPattern(DATE_FORMAT_STR_5);
  public static final DateTimeFormatter DATE_FORMAT_6 =
      DateTimeFormatter.ofPattern(DATE_FORMAT_STR_6);
  public static final DateTimeFormatter DATE_FORMAT_7 =
      DateTimeFormatter.ofPattern(DATE_FORMAT_STR_7);
  public static final DateTimeFormatter DATE_FORMAT_8 =
      DateTimeFormatter.ofPattern(DATE_FORMAT_STR_8);
  public static final DateTimeFormatter DATE_FORMAT_9 =
      DateTimeFormatter.ofPattern(DATE_FORMAT_STR_9);
  public static final DateTimeFormatter DATE_FORMAT_10 =
      DateTimeFormatter.ofPattern(DATE_FORMAT_STR_10);

  // retrieve no of days between startDate and endDate [LocalDateTime]
  public static Long getInterval(LocalDateTime startDate, LocalDateTime endDate) {
    if (!CommonUtil.isExist(startDate) || !CommonUtil.isExist(endDate)) {
      throw new RmsException(ExceptionCode.INVALID_DATE, "startDate and/or endDate is null");
    }
    Long diffInDays = ChronoUnit.DAYS.between(endDate, startDate);
    return diffInDays;
  }

  // retrieve no of days between startDate and endDate [LocalDate]
  public static Long getInterval(LocalDate startDate, LocalDate endDate) {
    if (!CommonUtil.isExist(startDate) || !CommonUtil.isExist(endDate)) {
      throw new RmsException(ExceptionCode.INVALID_DATE, "startDate and/or endDate is null");
    }
    return ChronoUnit.DAYS.between(startDate, endDate);
  }

  // add days to localDate
  public static LocalDate addDays(LocalDate date, Long noOfDays) {
    if (date == null || noOfDays == null) {
      return date;
    }
    return date.plusDays(noOfDays);
  }

  // add days to localDateTime
  public static LocalDateTime addDays(LocalDateTime date, Long noOfDays) {
    if (date == null || noOfDays == null) {
      return date;
    }
    return date.plusDays(noOfDays);
  }

  // get the latest date from 2 dates
  public static LocalDateTime getGreatestDate(LocalDateTime date1, LocalDateTime date2) {
    return date1.isAfter(date2) ? date1 : date2;
  }

  // get the older date from 2 dates
  public static LocalDateTime getLeastDate(LocalDateTime date1, LocalDateTime date2) {
    return date1.isBefore(date2) ? date1 : date2;
  }

  // formatting: format date into "dd/MM/yyyy"
  public static <T> String formatDateDdMmYyyy(T date) {
    if (!CommonUtil.isExist(date)) return null;
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    String formattedDate = "";

    if (date instanceof LocalDate) {
      LocalDate dateInLocalDate = (LocalDate) date;
      formattedDate = dateInLocalDate.format(formatter);
    } else if (date instanceof LocalDateTime) {
      LocalDateTime dateInLocalDateTime = (LocalDateTime) date;
      formattedDate = dateInLocalDateTime.format(formatter);
    }

    return formattedDate;
  }

  // set time to [23:59:59] to the date
  public static LocalDateTime endDate(LocalDate date) {
    return date.atTime(LocalTime.MAX);
  }

  // set time to [00:00:00] to the date
  public static LocalDateTime startDate(LocalDate date) {
    return date.atTime(LocalTime.MIN);
  }

  // check if date expired
  public static boolean checkDateExpired(LocalDateTime dateToCheck, LocalDateTime date) {
    return date.isAfter(dateToCheck);
  }

  // get years from date
  public static Long getYears(LocalDate date) {
    Long years = Math.abs(ChronoUnit.YEARS.between(date, LocalDate.now()));
    return years;
  }
}
