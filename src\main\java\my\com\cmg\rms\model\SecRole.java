package my.com.cmg.rms.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rms_sec_role")
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class SecRole extends BaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "rol_id")
  private Long rolId;

  @Column(name = "rol_shortdescription")
  private String rolShortdescription;

  @Column(name = "rol_longdescription")
  private String rolLongdescription;

  @Version
  @Column(name = "version", columnDefinition = "integer default 0")
  private Integer version;

  @Column(name = "iwp_sync_date")
  private LocalDateTime iwpSyncDate;

  @PreUpdate
  public void preUpdate() {
    this.setUpdatedDate(LocalDateTime.now());
  }
}