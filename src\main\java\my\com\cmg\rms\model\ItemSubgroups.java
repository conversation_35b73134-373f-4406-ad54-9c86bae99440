package my.com.cmg.rms.model;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_item_subgroups")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ItemSubgroups extends BaseEntity {

  @Id
  @Column(name = "itm_subgroup_seqno", unique = true, nullable = false)
  @SequenceGenerator(name = "item_subgroup_seq_no", sequenceName = "rm_item_subgroups_seq", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "item_subgroup_seq_no")
  private Long itmSubgroupSeqno;

  @Column(name = "itm_subgroup_code", length = 20, nullable = false)
  private String itmSubgroupCode;

  @Column(name = "subgroup_desc", length = 100)
  private String subgroupDesc;

  @Column(name = "status", length = 1)
  private String status;

  @Column(name = "itm_group_code", length = 10)
  private String itmGroupCode;

  @Column(name = "item_group")
  private Long itemGroup;

  @Column(name = "itm_cat_seqno")
  private Long itmCatSeqno;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "itm_cat_seqno", referencedColumnName = "itm_cat_seqno", insertable = false, updatable = false)
  private ItemCategories itemCategory;

  @Column(name = "iwp_sync_date")
  private LocalDateTime iwpSyncDate;
}