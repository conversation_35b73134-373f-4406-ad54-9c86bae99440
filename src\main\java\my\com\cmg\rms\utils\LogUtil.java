package my.com.cmg.rms.utils;

/**
 * @see DateUtil for date related utilities
 * @see TableUtil for table related utilities
 * @see PaginationUtil for pagination related utilities
 * @see LogUtil for loggers related utilities
 * @see CommonUtil for common related utilities
 */
public class LogUtil {

  public static final String ENTRY = "ENTRY | {}";

  public static final String ENTRY_SERVICE = "ENTRY SERVICE | {}";

  public static final String ENTRY_JOOQ = "ENTRY JOOQ | {}";

  public static final String ENTRY_UTIL = "ENTRY UTIL | {}";

  public static final String EXIT = "EXIT | {}";

  public static final String EXIT_SERVICE = "EXIT SERVICE | {}";

  public static final String EXIT_JOOQ = "EXIT JOOQ | {}";

  public static final String EXIT_UTIL = "EXIT UTIL | {}";

  public static final String QUERY = "QUERY | {}";

  public static final String QUERY_RESULT_UPDATE = "Update successful. Rows affected: {}";

  public static final String QUERY_RESULT_LIST = "Query successful. No. of rows returned: {}";

  public static final String QUERY_RESULT_DELETE = "Delete successful. Rows affected: {}";

  public static final String UNIT_TEST = "UNIT TEST | {}";

  public static final String ENTRY_CONTROLLER = "ENTRY CONTROLLER | {}";

  public static final String ENTRY_REPOSITORY = "ENTRY REPOSITORY | {}";

  public static final String EXIT_CONTROLLER = "EXIT CONTROLLER | {}";

  public static final String EXIT_REPOSITORY = "EXIT REPOSITORY | {}";
}
