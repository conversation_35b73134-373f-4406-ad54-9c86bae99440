package my.com.cmg.rms.utils;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Aspect
@Component
public class LoggingAspect {

  @Pointcut("execution(* my.com.cmg.rms..*(..))")
  public void applicationPackagePointcut() {}

  @Around("applicationPackagePointcut()")
  public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
    Class<?> declaringType = joinPoint.getSignature().getDeclaringType();
    String entryLayer = determineEntryLayer(declaringType);
    String methodName = joinPoint.getSignature().getName();
    String className = joinPoint.getSignature().getDeclaringTypeName();
    // Entering
    log.info(entryLayer, String.format("Method: %s, File: %s", methodName, className));

    Object result = joinPoint.proceed();
    String exitLayer = determineExitLayer(declaringType);
    // Exiting
    log.info(exitLayer, String.format("Method: %s, File: %s", methodName, className));
    return result;
  }

  private String determineEntryLayer(Class<?> clazz) {
    if (AnnotationUtils.findAnnotation(clazz, RestController.class) != null
        || AnnotationUtils.findAnnotation(clazz, Controller.class) != null) {
      return LogUtil.ENTRY_CONTROLLER;
    } else if (AnnotationUtils.findAnnotation(clazz, Service.class) != null) {
      return LogUtil.ENTRY_SERVICE;
    } else if (AnnotationUtils.findAnnotation(clazz, Repository.class) != null) {
      return LogUtil.ENTRY_REPOSITORY;
    } else {
      return LogUtil.ENTRY;
    }
  }

  private String determineExitLayer(Class<?> clazz) {
    if (AnnotationUtils.findAnnotation(clazz, RestController.class) != null
        || AnnotationUtils.findAnnotation(clazz, Controller.class) != null) {
      return LogUtil.EXIT_CONTROLLER;
    } else if (AnnotationUtils.findAnnotation(clazz, Service.class) != null) {
      return LogUtil.EXIT_SERVICE;
    } else if (AnnotationUtils.findAnnotation(clazz, Repository.class) != null) {
      return LogUtil.EXIT_REPOSITORY;
    } else {
      return LogUtil.EXIT;
    }
  }
}
