# RMS - BACKEND (Request Management System for Backend)
- This is a Java-based backend project utilizing **JPA** (Java Persistence API) for ORM and **jOOQ** (Java Object Oriented Querying) for type-safe SQL query building.

## 🔧 Tech Stack

- Java 17+
- Spring Boot
- JPA (Hibernate)
- jOOQ
- PostgreSQL
- Gradle

## 📁 Project Structure
```bash
src/
├── main/
│   ├── java/
│   │   └── my/com/cmg/rms/
│   │       ├── controller/
│   │       ├── dto/
│   │       ├── exception/
│   │       ├── mapper/
│   │       ├── model/          # entity
│   │       ├── repository/
│   │       │   ├── jpa/        # JPA Repositories
│   │       │   └── jooq/       # jOOQ Custom Queries
│   │       ├── service/        # services
│   │       └── utils/
│   └── resources/
│       └── application.properties
```

## Configuration
### application.properties 
- development only:
  - spring.application.name=rms
  - spring.datasource.url=************************************
  - spring.datasource.username=ihisblank
  - spring.datasource.password=ihisblank

## Run the application
  ```shell
   ./gradlew bootrun
  ```

## Clean the application
  ```shell
   ./gradlew clean
  ```

## Build the application
```shell
./gradlew build
```



## List & Pagination Structure
- List structure consist of 2 parts (listing and pagination)
- The list and pagination will be separated to optimize load
