package my.com.cmg.rms.repository.jooq;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.noCondition;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.RequestDtlDTO;
import my.com.cmg.rms.dto.RequestHeaderDTO;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.dto.requestListing.RequestListDTO;
import my.com.cmg.rms.dto.requestListing.RequestListSearchDTO;
import my.com.cmg.rms.dto.viewDetail.FacilityMasterRequestDTO;
import my.com.cmg.rms.dto.viewDetail.ItemMasterRequestDTO;
import my.com.cmg.rms.dto.viewDetail.ItemPackagingRequestDTO;
import my.com.cmg.rms.dto.viewDetail.SupplierMasterRequestDTO;
import my.com.cmg.rms.utils.JooqUtil;
import my.com.cmg.rms.utils.LogUtil;
import my.com.cmg.rms.utils.TableUtil;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record17;
import org.jooq.Record18;
import org.jooq.Record9;
import org.jooq.Select;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class RequestRepositoryJooq {
  DSLContext dsl;

  public RequestRepositoryJooq(DSLContext dsl) {
    this.dsl = dsl;
  }

  public List<RequestListDTO> getRequestList(
      RequestListSearchDTO requestDTO, PaginationRequestDTO pageRq) {

    // condition
    Condition condition = noCondition();
    // TODO: search by division

    condition =
        JooqUtil.andCondition(
            condition, field("request_no"), Field::containsIgnoreCase, requestDTO.requestNo());
    condition =
        JooqUtil.andCondition(
            condition, field("facility_seqno"), Field::eq, requestDTO.facilitySeqno());
    condition =
        JooqUtil.andCondition(
            condition, field("request_type"), Field::eq, requestDTO.requestType());
    condition =
        JooqUtil.andCondition(
            condition, field("title"), Field::containsIgnoreCase, requestDTO.title());
    condition =
        JooqUtil.andCondition(condition, field("assigned_to"), Field::eq, requestDTO.assignedTo());
    condition =
        JooqUtil.andCondition(condition, field("category"), Field::eq, requestDTO.category());
    condition =
        JooqUtil.andCondition(
            condition, field("requested_date"), Field::ge, requestDTO.requestDateFrom());
    condition =
        JooqUtil.andCondition(
            condition, field("requested_date"), Field::le, requestDTO.requestDateTo());
    condition = JooqUtil.andCondition(condition, field("status"), Field::eq, requestDTO.status());

    // fields
    Field<Long> requestHdrSeqno = field("request_hdr_seqno", Long.class).as("requestHdrSeqno");
    Field<String> requestNo = field("request_no", String.class).as("requestNo");
    Field<Long> facilitySeqno = field("facility_seqno", Long.class).as("facilitySeqno");
    Field<String> requestType = field("request_type", String.class).as("requestType");
    Field<String> title = field("title", String.class).as("title");
    Field<String> assignedTo = field("assigned_to", String.class).as("assignedTo");
    Field<String> category = field("category", String.class).as("category");
    Field<LocalDate> requestedDate = field("requested_date", LocalDate.class).as("requestDate");
    Field<String> status = field("status", String.class).as("status");

    Select<Record9<Long, String, Long, String, String, String, String, LocalDate, String>> query =
        dsl.select(
                requestHdrSeqno,
                requestNo,
                facilitySeqno,
                requestType,
                title,
                assignedTo,
                category,
                requestedDate,
                status)
            .from(TableUtil.table(TableUtil.RM_REQUEST_HDR, "HDR"))
            .where(condition)
            .orderBy(CoreUtilsRepositoryJooq.getOrderByField(pageRq.sort(), pageRq.sortDirection()))
            .offset((pageRq.page() - 1) * pageRq.size())
            .limit(pageRq.size());

    log.info(LogUtil.QUERY, query);
    List<RequestListDTO> result = query.fetchInto(RequestListDTO.class);

    return result;
  }

  public Long getRequestListCount(RequestListSearchDTO requestDTO) {

    // condition
    Condition condition = noCondition();
    // TODO: search by division

    condition =
        JooqUtil.andCondition(
            condition, field("request_no"), Field::containsIgnoreCase, requestDTO.requestNo());
    condition =
        JooqUtil.andCondition(
            condition, field("facility_seqno"), Field::eq, requestDTO.facilitySeqno());
    condition =
        JooqUtil.andCondition(
            condition, field("request_type"), Field::eq, requestDTO.requestType());
    condition =
        JooqUtil.andCondition(
            condition, field("title"), Field::containsIgnoreCase, requestDTO.title());
    condition =
        JooqUtil.andCondition(condition, field("assigned_to"), Field::eq, requestDTO.assignedTo());
    condition =
        JooqUtil.andCondition(condition, field("category"), Field::eq, requestDTO.category());
    condition =
        JooqUtil.andCondition(
            condition, field("requested_date"), Field::ge, requestDTO.requestDateFrom());
    condition =
        JooqUtil.andCondition(
            condition, field("requested_date"), Field::le, requestDTO.requestDateTo());
    condition = JooqUtil.andCondition(condition, field("status"), Field::eq, requestDTO.status());

    Select<Record1<Integer>> query =
        dsl.selectCount().from(TableUtil.table(TableUtil.RM_REQUEST_HDR)).where(condition);

    log.info(LogUtil.QUERY, query);
    Long result = query.fetchOneInto(Long.class);

    return result;
  }

  public RequestHeaderDTO getRequestHeader(Long requestHdrSeqnoReq) {
    Condition condition = field("hdr.request_hdr_seqno").eq(requestHdrSeqnoReq);
    log.info(LogUtil.QUERY, condition);

    var result =
        dsl.select(
                field("hdr.request_hdr_seqno", Long.class).as("requestHdrSeqno"),
                field("hdr.request_no", String.class).as("requestNo"),
                field("hdr.request_type", String.class).as("requestType"),
                field("hdr.request_type_desc", String.class).as("requestTypeDesc"),
                field("hdr.category", String.class).as("category"),
                field("hdr.category_desc", String.class).as("categoryDesc"),
                field("hdr.sub_category", String.class).as("subCategory"),
                field("hdr.sub_category_desc", String.class).as("subCategoryDesc"),
                field("hdr.title", String.class).as("title"),
                field("hdr.reference", String.class).as("reference"),
                field("hdr.intention", String.class).as("intention"),
                field("hdr.intention_desc", String.class).as("intentionDesc"),
                field("hdr.reason", String.class).as("reason"),
                field("hdr.facility_seqno", Long.class).as("facilitySeqno"), // fixed type
                field("hdr.facility_name", String.class).as("facilityName"),
                field("hdr.requested_by_seqno", Long.class).as("requestedBySeqno"),
                field("hdr.requested_by_name", String.class).as("requestedByName"),
                field("hdr.requested_date", String.class).as("requestedDate"),
                field("hdr.assigned_to", String.class).as("assignedTo"),
                field("hdr.assigned_to_desc", String.class).as("assignedToDesc"),
                field("hdr.assigned_from", String.class).as("assignedFrom"),
                field("hdr.assigned_from_desc", String.class).as("assignedFromDesc"),
                field("hdr.status", String.class).as("status"),
                field("hdr.status_desc", String.class).as("statusDesc"),
                field("hdr.reject_reason", String.class).as("rejectReason"),
                field("hdr.created_date", LocalDateTime.class).as("createdDate"),
                field("hdr.updated_date", LocalDateTime.class).as("updatedDate"),
                field("hdr.active_flag", String.class).as("activeFlag"),
                field("hdr.created_by", Long.class).as("createdBy"),
                field("hdr.updated_by", Long.class).as("updatedBy"),

                // Detail fields
                field("dtl.request_dtl_seqno", Long.class).as("requestDtlSeqno"),
                field("dtl.trans_seqno", Long.class).as("transSeqno"),
                field("dtl.trans_code", String.class).as("transCode"),
                field("dtl.trans_name", String.class).as("transName"),
                field("dtl.trans_type", String.class).as("transType"),
                field("dtl.trans_details", String.class).as("transDetails"),
                field("dtl.created_by", Long.class).as("dtlCreatedBy"),
                field("dtl.updated_by", Long.class).as("dtlUpdatedBy"))
            .from(TableUtil.table(TableUtil.RM_REQUEST_HDR, "hdr"))
            .leftJoin(TableUtil.table(TableUtil.RM_REQUEST_DTL, "dtl"))
            .on(field("hdr.request_hdr_seqno").eq(field("dtl.request_hdr_seqno")))
            .where(condition)
            .fetch();

    if (result.isEmpty()) {
      log.warn("No request found for requestHdrSeqno: {}", requestHdrSeqnoReq);
      return null;
    }

    var r = result.get(0);

    RequestDtlDTO[] details =
        result.stream()
            .filter(row -> row.get("requestDtlSeqno", Long.class) != null)
            .map(
                row ->
                    new RequestDtlDTO(
                        row.get("requestHdrSeqno", Long.class),
                        row.get("transSeqno", Long.class),
                        row.get("transCode", String.class),
                        row.get("transName", String.class),
                        row.get("transType", String.class),
                        row.get("transDetails", String.class),
                        row.get("dtlCreatedBy", Long.class),
                        row.get("dtlUpdatedBy", Long.class)))
            .toArray(RequestDtlDTO[]::new);

    return new RequestHeaderDTO(
        r.get("requestHdrSeqno", Long.class),
        r.get("requestNo", String.class),
        r.get("requestType", String.class),
        r.get("requestTypeDesc", String.class),
        r.get("category", String.class),
        r.get("categoryDesc", String.class),
        r.get("subCategory", String.class),
        r.get("subCategoryDesc", String.class),
        r.get("title", String.class),
        r.get("reference", String.class),
        r.get("intention", String.class),
        r.get("intentionDesc", String.class),
        r.get("reason", String.class),
        r.get("facilitySeqno", Long.class),
        r.get("facilityName", String.class),
        r.get("requestedBySeqno", Long.class),
        r.get("requestedByName", String.class),
        r.get("requestedDate", String.class),
        r.get("assignedTo", String.class),
        r.get("assignedToDesc", String.class),
        r.get("assignedFrom", String.class),
        r.get("assignedFromDesc", String.class),
        r.get("status", String.class),
        r.get("statusDesc", String.class),
        r.get("rejectReason", String.class),
        r.get("createdDate", LocalDateTime.class) != null
            ? r.get("createdDate", LocalDateTime.class)
                .toInstant(java.time.ZoneOffset.UTC)
                .toEpochMilli()
            : null,
        r.get("updatedDate", LocalDateTime.class) != null
            ? r.get("updatedDate", LocalDateTime.class)
                .toInstant(java.time.ZoneOffset.UTC)
                .toEpochMilli()
            : null,
        r.get("activeFlag", String.class),
        r.get("createdBy", Long.class),
        r.get("updatedBy", Long.class),
        details);
  }

  public ItemMasterRequestDTO getItemMasterRequest(Long transSeqno) {

    // condition:
    Condition condition = noCondition();
    condition = condition.and(field("item_req_seqno").eq(transSeqno));

    // fields from ITEMS
    Field<Long> itemReqSeqno = field("item_req_seqno", Long.class).as("itemReqSeqno");
    Field<String> itemGroupCode = field("item_group_code", String.class).as("itemGroupCode");
    Field<Long> genericNameSeqno = field("generic_name_seqno", Long.class).as("genericNameSeqno");
    Field<String> otherActiveIngredient =
        field("other_active_ingredient", String.class).as("otherActiveIngredient");
    Field<String> strength = field("strength", String.class).as("strength");
    Field<Long> dosageSeqno = field("dosage_seqno", Long.class).as("dosageSeqno");
    Field<String> itemName = field("item_name", String.class).as("itemName");
    Field<Long> itemCatSeqno = field("itm_cat_seqno", Long.class).as("itemCatSeqno");
    Field<Long> itemSubgroupSeqno = field("itm_subgroup_seqno", Long.class).as("itemSubgroupSeqno");
    Field<Long> freqSeqno = field("freq_seqno", Long.class).as("freqSeqno");
    Field<String> administrationRoute =
        field("administration_route", String.class).as("administrationRoute");
    Field<String> drugIndication = field("drug_indication", String.class).as("drugIndication");
    Field<String> rpItemTypeCode = field("rp_item_type_code", String.class).as("rpItemTypeCode");
    Field<Long> itemPackagingSeqno =
        field("item_packaging_seqno", Long.class).as("itemPackagingSeqno");
    Field<Long> skuSeqno = field("sku_seqno", Long.class).as("skuSeqno");
    Field<Long> pkuSeqno = field("pku_seqno", Long.class).as("pkuSeqno");
    Field<BigDecimal> conversionFactorNum =
        field("conversion_factor_num", BigDecimal.class).as("conversionFactorNum");
    Field<String> packagingDesc = field("packaging_desc", String.class).as("packagingDesc");
    Field<String> mdcNo = field("mdc_no", String.class).as("mdcNo");
    Field<Long> productSeqno = field("product_seqno", Long.class).as("productSeqno");
    Field<String> productName = field("product_name", String.class).as("productName");
    Field<String> manufacturedName =
        field("manufactured_name", String.class).as("manufacturedName");
    Field<String> importerName = field("importer_name", String.class).as("importerName");
    Field<String> manufacturedAddress =
        field("manufactured_address", String.class).as("manufacturedAddress");
    Field<String> importerAddress = field("importer_address", String.class).as("importerAddress");
    Field<String> gtinNo = field("gtin_no", String.class).as("gtinNo");
    Field<String> mdaNo = field("mda_no", String.class).as("mdaNo");

    var query =
        dsl.select(
                itemReqSeqno,
                itemGroupCode,
                genericNameSeqno,
                otherActiveIngredient,
                strength,
                dosageSeqno,
                itemName,
                itemCatSeqno,
                itemSubgroupSeqno,
                freqSeqno,
                administrationRoute,
                drugIndication,
                rpItemTypeCode,
                itemPackagingSeqno,
                skuSeqno,
                pkuSeqno,
                conversionFactorNum,
                packagingDesc,
                mdcNo,
                productSeqno,
                productName,
                manufacturedName,
                importerName,
                manufacturedAddress,
                importerAddress,
                gtinNo,
                mdaNo)
            .from(TableUtil.table(TableUtil.RM_REQUEST_ITEMS, "ITM"))
            .where(condition)
            .limit(1);

    log.info(LogUtil.QUERY, query);

    ItemMasterRequestDTO result = query.fetchOneInto(ItemMasterRequestDTO.class);

    return result;
  }

  public ItemPackagingRequestDTO getItemPackagingRequest(Long transSeqno) {

    // condition
    Condition condition = noCondition();
    condition = condition.and(field("item_packaging_req_seqno").eq(transSeqno));

    // fields from ITEM_PACKAGING
    Field<Long> itemPackagingReqSeqno =
        field("item_packaging_req_seqno", Long.class).as("itemPackagingReqSeqno");
    Field<Long> itemSeqno = field("item_seqno", Long.class).as("itemSeqno");
    Field<String> itemName = field("item_name", String.class).as("itemName");
    Field<String> itemCode = field("item_code", String.class).as("itemCode");
    Field<String> itemPackagingName =
        field("item_packaging_name", String.class).as("itemPackagingName");
    Field<Long> skuSeqno = field("sku_seqno", Long.class).as("skuSeqno");
    Field<Long> pkuSeqno = field("pku_seqno", Long.class).as("pkuSeqno");
    Field<BigDecimal> conversionFactor =
        field("conversion_factor", BigDecimal.class).as("conversionFactor");
    Field<String> packagingDesc = field("packaging_desc", String.class).as("packagingDesc");
    Field<String> productList = field("product_list", String.class).as("productList");
    Field<Long> productSeqno = field("product_seqno", Long.class).as("productSeqno");
    Field<String> productName = field("product_name", String.class).as("productName");
    Field<String> manufacturedName =
        field("manufactured_name", String.class).as("manufacturedName");
    Field<String> importerName = field("importer_name", String.class).as("importerName");
    Field<String> manufacturedAddress =
        field("manufactured_address", String.class).as("manufacturedAddress");
    Field<String> importerAddress = field("importer_address", String.class).as("importerAddress");
    Field<String> gtinNo = field("gtin_no", String.class).as("gtinNo");
    Field<String> mdaNo = field("mda_no", String.class).as("mdaNo");

    Select<
            Record18<
                Long,
                Long,
                String,
                String,
                String,
                Long,
                Long,
                BigDecimal,
                String,
                String,
                Long,
                String,
                String,
                String,
                String,
                String,
                String,
                String>>
        query =
            dsl.select(
                    itemPackagingReqSeqno,
                    itemSeqno,
                    itemName,
                    itemCode,
                    itemPackagingName,
                    skuSeqno,
                    pkuSeqno,
                    conversionFactor,
                    packagingDesc,
                    productList,
                    productSeqno,
                    productName,
                    manufacturedName,
                    importerName,
                    manufacturedAddress,
                    importerAddress,
                    gtinNo,
                    mdaNo)
                .from(TableUtil.table(TableUtil.RM_REQUEST_ITEM_PACKAGING, "ITM"))
                .where(condition)
                .limit(1);

    log.info(LogUtil.QUERY, query);
    ItemPackagingRequestDTO result = query.fetchOneInto(ItemPackagingRequestDTO.class);

    return result;
  }

  public FacilityMasterRequestDTO getFacilityRequest(Long transSeqno) {

    // condition
    Condition condition = noCondition();
    condition = condition.and(field("facility_req_seqno").eq(transSeqno));

    // fields from FACILITY
    Field<Long> facilityReqSeqno = field("facility_req_seqno", Long.class).as("facilityReqSeqno");
    Field<String> facilityReqName = field("facility_req_name", String.class).as("facilityReqName");
    Field<String> facilityReqGroup =
        field("facility_req_group", String.class).as("facilityReqGroup");
    Field<String> ministry = field("ministry", String.class).as("ministry");
    Field<String> facilityReqCategory =
        field("facility_req_category", String.class).as("facilityReqCategory");
    Field<String> facilityReqType = field("facility_req_type", String.class).as("facilityReqType");
    Field<String> address1 = field("address1", String.class).as("address1");
    Field<String> address2 = field("address2", String.class).as("address2");
    Field<String> address3 = field("address3", String.class).as("address3");
    Field<String> city = field("city", String.class).as("city");
    Field<String> postcode = field("postcode", String.class).as("postcode");
    Field<String> state = field("state", String.class).as("state");
    Field<String> country = field("country", String.class).as("country");
    Field<String> mobilePhone = field("mobile_phone", String.class).as("mobilePhone");
    Field<String> email = field("email", String.class).as("email");
    Field<String> contactPerson = field("contact_person", String.class).as("contactPerson");
    Field<String> contactNo = field("contact_no", String.class).as("contactNo");

    Select<
            Record17<
                Long,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String>>
        query =
            dsl.select(
                    facilityReqSeqno,
                    facilityReqName,
                    facilityReqGroup,
                    ministry,
                    facilityReqCategory,
                    facilityReqType,
                    address1,
                    address2,
                    address3,
                    city,
                    postcode,
                    state,
                    country,
                    mobilePhone,
                    email,
                    contactPerson,
                    contactNo)
                .from(TableUtil.table(TableUtil.RM_REQUEST_FACILITY, "FAC"))
                .where(condition)
                .limit(1);

    log.info(LogUtil.QUERY, query);
    FacilityMasterRequestDTO result = query.fetchOneInto(FacilityMasterRequestDTO.class);

    return result;
  }

  public SupplierMasterRequestDTO getSupplierRequest(Long transSeqno) {

    // condition: cari berdasarkan supplier_req_seqno = transSeqno
    Condition condition = field("supplier_req_seqno").eq(transSeqno);

    // fields from SUPPLIER
    Field<Long> supplierReqSeqno = field("supplier_req_seqno", Long.class).as("supplierReqSeqno");
    Field<String> supplierReqName = field("supplier_req_name", String.class).as("supplierReqName");
    Field<String> companyRegNo = field("company_reg_no", String.class).as("companyRegNo");
    Field<LocalDate> regExpiryDate = field("reg_expiry_date", LocalDate.class).as("regExpiryDate");
    Field<String> trsRegNo = field("trs_reg_no", String.class).as("trsRegNo");
    Field<String> companyStatus = field("company_status", String.class).as("companyStatus");
    Field<String> address1 = field("address1", String.class).as("address1");
    Field<String> address2 = field("address2", String.class).as("address2");
    Field<String> address3 = field("address3", String.class).as("address3");
    Field<String> city = field("city", String.class).as("city");
    Field<String> postcode = field("postcode", String.class).as("postcode");
    Field<String> state = field("state", String.class).as("state");
    Field<String> country = field("country", String.class).as("country");
    Field<String> mobilePhone = field("mobile_phone", String.class).as("mobilePhone");
    Field<String> email = field("email", String.class).as("email");
    Field<String> contactPerson = field("contact_person", String.class).as("contactPerson");
    Field<String> contactNo = field("contact_no", String.class).as("contactNo");

    var query =
        dsl.select(
                supplierReqSeqno,
                supplierReqName,
                companyRegNo,
                regExpiryDate,
                trsRegNo,
                companyStatus,
                address1,
                address2,
                address3,
                city,
                postcode,
                state,
                country,
                mobilePhone,
                email,
                contactPerson,
                contactNo)
            .from(TableUtil.table(TableUtil.RM_REQUEST_SUPPLIER, "SUP"))
            .where(condition)
            .limit(1);

    log.info(LogUtil.QUERY, query);

    SupplierMasterRequestDTO result = query.fetchOneInto(SupplierMasterRequestDTO.class);
    return result;
  }
}
