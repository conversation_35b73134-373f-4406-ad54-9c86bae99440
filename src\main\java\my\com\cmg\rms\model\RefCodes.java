package my.com.cmg.rms.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_ref_codes")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RefCodes extends BaseEntity {

  @Id
  @Column(name = "rc_seq", unique = true, nullable = false)
  @SequenceGenerator(
      name = "ref_code_seq_no",
      sequenceName = "rm_ref_codes_seq",
      allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ref_code_seq_no")
  private Long rcSeq;

  @Column(name = "rc_domain", length = 50)
  private String rcDomain;

  @Column(name = "rc_value", length = 500)
  private String rcValue;

  @Column(name = "rc_desc", length = 200)
  private String rcDescription;

  @Column(name = "rc_remarks", length = 200)
  private String rcRemarks;

  @Column(name = "rc_smrp_value")
  private String rcSmrpValue;

  @Column(name = "iwp_sync_date")
  private LocalDateTime iwpSyncDate;

  @Column(name = "rc_smrp_v2_value")
  private String rcSmrpV2Value;
}
