package my.com.cmg.rms.model;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_admin_routes")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AdminRoutes extends BaseEntity {

  @Id
  @Column(name = "route_seqno", unique = true, nullable = false)
  @SequenceGenerator(name = "admin_route_seq_no", sequenceName = "rm_admin_routes_seq", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "admin_route_seq_no")
  private Long routeSeqno;

  @Column(name = "route_code", length = 15, nullable = false)
  private String routeCode;

  @Column(name = "route_desc", length = 60, nullable = false)
  private String routeDesc;

  @Column(name = "route_local_desc", length = 100)
  private String routeLocalDesc;

  @Column(name = "route_status", length = 10)
  private String routeStatus;

  @Column(name = "label", length = 30)
  private String label;

  @Column(name = "mims_route_desc", length = 100)
  private String mimsRouteDesc;

  @Column(name = "administration_factor")
  private Integer administrationFactor;

  @Column(name = "route_factor")
  private Integer routeFactor;

  @Column(name = "iwp_sync_date")
  private LocalDateTime iwpSyncDate;

  @Column(name = "method_malay", length = 50)
  private String methodMalay;

  @Column(name = "method_desc_malay", length = 100)
  private String methodDescMalay;

  @Column(name = "method_english", length = 50)
  private String methodEnglish;

  @Column(name = "method_desc_english", length = 100)
  private String methodDescEnglish;
}