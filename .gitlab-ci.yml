stages:
  - build-check
  - build
  - deploy
  - cleanup
  
variables:
  PROJECT_NAME: rms
  IMAGE_NAME: **********:5000/rms
  IMAGE_TAG: dev
  APP_NAME: rms
  NAMESPACE: dev

# BEGIN job-specific configuration
build:
  stage: build
  image: epicsoft/openjdk:jdk17
  services:
    - docker:dind
  script:
    - ./gradlew bootBuildImage
    - docker tag $IMAGE_NAME $IMAGE_NAME:$IMAGE_TAG
    - docker push $IMAGE_NAME:$IMAGE_TAG
  rules:
    - if: $CI_COMMIT_BRANCH == "dev"

deploy:
  stage: deploy
  image: dtzar/helm-kubectl:latest
  before_script:
    - echo "$KUBE_CONFIG_DEV" > kubeconfig.yaml
  script:
    - kubectl --kubeconfig=kubeconfig.yaml set image deployment/$APP_NAME $APP_NAME=$IMAGE_NAME:$IMAGE_TAG -n $NAMESPACE
    - kubectl --kubeconfig=kubeconfig.yaml rollout restart deployment/$APP_NAME -n $NAMESPACE
  needs:
    - build

  rules:
    - if: $CI_COMMIT_BRANCH == "dev"

cleanup: 
  stage: cleanup
  image: epicsoft/openjdk:jdk17
  services:
    - docker:dind
  script:
    - docker rmi $IMAGE_NAME
  rules:
    - if: $CI_COMMIT_BRANCH == "dev"
  allow_failure: true


build-check:
  image: openjdk:17-jdk-slim
  stage: build-check
  script:
    - ./gradlew bootJar
  tags:
    - merge_request
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'


#
# END job-specific configuration

