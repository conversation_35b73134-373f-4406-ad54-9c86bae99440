package my.com.cmg.rms.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rms_sec_user")
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class SecUser extends BaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "usr_id")
  private Long usrId;

  @Column(name = "username", unique = true, nullable = false)
  private String username;

  @Column(name = "usr_full_name")
  private String usrFullName;

  @Column(name = "email", unique = true)
  private String email;

  @Column(name = "division_id")
  private Long divisionId;

  @Column(name = "division_name")
  private String divisionName;

  @Column(name = "usr_status")
  private String usrStatus;

  @Column(name = "last_login_date")
  private LocalDateTime lastLoginDate;

  @Column(name = "password_changed_date")
  private LocalDateTime passwordChangedDate;

  @Column(name = "iwp_sync_date")
  private LocalDateTime iwpSyncDate;
}