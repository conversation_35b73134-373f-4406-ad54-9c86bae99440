package my.com.cmg.rms.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import my.com.cmg.rms.constant.RmsConstant;
import org.springframework.stereotype.Service;

/**
 * @see DateUtil for date related utilities
 * @see TableUtil for table related utilities
 * @see PaginationUtil for pagination related utilities
 * @see LogUtil for loggers related utilities
 * @see CommonUtil for common related utilities
 */
@Service
public class CommonUtil {

  // transform character to boolean : trueCondition = [Y/A], falseCondition = [N/I]
  public static Boolean toBoolean(Character flag) {
    final List<Character> trueFlag =
        Arrays.asList(RmsConstant.ACTIVE_FLAG_TRUE, RmsConstant.BOOLEAN_TRUE);

    return flag != null && trueFlag.contains(flag);
  }

  // transform character to boolean. if no value match, return null : trueCondition = [Y/A],
  // falseCondition = [N/I]
  public static Boolean toBooleanNull(Character flag) {
    final List<Character> trueFlag =
        Arrays.asList(RmsConstant.ACTIVE_FLAG_TRUE, RmsConstant.BOOLEAN_TRUE);

    final List<Character> falseFlag =
        Arrays.asList(RmsConstant.ACTIVE_FLAG_FALSE, RmsConstant.BOOLEAN_FALSE);

    if (trueFlag.contains(flag)) {
      return true;
    }

    if (falseFlag.contains(flag)) {
      return false;
    }

    return null;
  }

  // transform boolean to character [Y/N]
  public static Character toCharacter(Boolean flag) {
    if (flag != null && flag) {
      return RmsConstant.BOOLEAN_TRUE;
    } else {
      return RmsConstant.BOOLEAN_FALSE;
    }
  }

  // transform boolean to character [A/I]
  public static Character toAorICharacter(Boolean flag) {
    if (flag != null && flag) {
      return RmsConstant.ACTIVE_FLAG_TRUE;
    } else {
      return RmsConstant.ACTIVE_FLAG_FALSE;
    }
  }

  // transform optional data to actual response
  public static <T> Optional<T> getOptionalValue(T value) {
    return Optional.ofNullable(value);
  }

  // round double value
  public static Double round(Double roundValue) {
    roundValue = roundValue != null ? roundValue : 0D;
    return Double.valueOf(Math.ceil(roundValue));
  }

  // formatting: remove trailing zero and convert to string
  public static String numberFormatWithoutDecimalIfZero(Double value) {
    DecimalFormat nf = new DecimalFormat("#.##");
    return nf.format(value).trim();
  }

  // formatting: format bigDecimal into 2 decimal point
  public static BigDecimal formatBigDecimal(BigDecimal value) {
    if (value != null) {
      return value.setScale(2, RoundingMode.HALF_UP);
    } else {
      return BigDecimal.ZERO;
    }
  }

  // formatting: format bigDecimal into 4 decimal point
  public static BigDecimal formatBigDecimalRoundup4Decimal(BigDecimal value) {
    if (value != null) {
      return value.setScale(4, RoundingMode.HALF_UP);
    } else {
      return BigDecimal.ZERO;
    }
  }

  // convert all numeric value to double. Any null value will be converted to 0
  public static <T> Double toDouble(T value) {
    if (value != null) {
      if (value instanceof Long) {
        return ((Long) value).doubleValue();
      } else if (value instanceof Float) {
        return ((Float) value).doubleValue();
      } else if (value instanceof BigDecimal) {
        return ((BigDecimal) value).doubleValue();
      } else if (value instanceof Integer) {
        return ((Integer) value).doubleValue();
      } else if (value instanceof Double) {
        return (Double) value;
      } else if (value instanceof String) {
        String val = (String) value;
        return Double.parseDouble(val.trim());
      }
    }
    return 0d;
  }

  // convert all numeric value to double. Any null value will be converted to null
  public static <T> Double toDoubleElseNull(T value) {
    if (value != null) {
      if (value instanceof Long) {
        return ((Long) value).doubleValue();
      } else if (value instanceof Float) {
        return ((Float) value).doubleValue();
      } else if (value instanceof BigDecimal) {
        return ((BigDecimal) value).doubleValue();
      } else if (value instanceof Integer) {
        return ((Integer) value).doubleValue();
      } else if (value instanceof Double) {
        return (Double) value;
      } else if (value instanceof String) {
        String val = (String) value;
        return Double.parseDouble(val.trim());
      }
    }
    return null;
  }

  // convert all numeric value to BigDecimal. Any null value will be converted to 0
  public static <T> BigDecimal toBigDecimal(T value) {
    if (value != null) {
      if (value instanceof Long) {
        return BigDecimal.valueOf((Long) value);
      } else if (value instanceof Float) {
        return new BigDecimal(Float.toString((Float) value));
      } else if (value instanceof BigDecimal) {
        return (BigDecimal) value;
      } else if (value instanceof Integer) {
        return BigDecimal.valueOf((Integer) value);
      } else if (value instanceof Double) {
        return BigDecimal.valueOf((Double) value);
      } else if (value instanceof String) {
        String val = (String) value;
        return new BigDecimal(val.trim());
      }
    }
    return BigDecimal.ZERO;
  }

  // convert all numeric value to Long. Any null value will be converted to 0
  public static <T> Long toLong(T value) {
    if (value != null) {
      if (value instanceof Long) {
        return (Long) value;
      } else if (value instanceof Integer) {
        return ((Integer) value).longValue();
      } else if (value instanceof Float) {
        return ((Float) value).longValue();
      } else if (value instanceof Double) {
        return ((Double) value).longValue();
      } else if (value instanceof BigDecimal) {
        return ((BigDecimal) value).longValue();
      } else if (value instanceof String) {
        String val = (String) value;
        return Long.parseLong(val.trim());
      }
    }
    return 0L;
  }

  // convert all value to String. Any null value will be converted to empty string
  public static <T> String toString(T value) {
    if (value != null) {
      if (value instanceof String) {
        return (String) value;
      }
      return value.toString();
    }
    return "";
  }

  // check if listing exist
  public static <T> Boolean isListExist(List<T> list) {
    return list != null && list.size() > 0;
  }

  // check if object/field exist
  public static <T> Boolean isExist(T value) {
    if (value == null) {
      return false;
    }

    if (value instanceof String) {
      if (((String) value).isBlank()) {
        return false;
      }
    }

    return true;
  }

  // check if object/field is empty
  public static <T> Boolean isEmpty(T value) {
    if (value == null) {
      return true;
    }
    if (value instanceof String) {
      return ((String) value).isBlank();
    }
    return false;
  }

  // remove any trailing zero from BigDecimal
  public static BigDecimal removeTrailingZero(BigDecimal value) {
    if (value != null) {
      return value.stripTrailingZeros();
    } else {
      return value;
    }
  }

  // validate empty field. if empty will return null
  public static <T> T validateValue(T value) {
    return isExist(value) ? value : null;
  }
}
