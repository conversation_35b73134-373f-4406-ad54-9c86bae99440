package my.com.cmg.rms.service.impl;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.security.AuthUserDTO;
import my.com.cmg.rms.dto.security.SecUserDTO;
import my.com.cmg.rms.model.SecUser;
import my.com.cmg.rms.repository.jpa.SecUserRepository;
import my.com.cmg.rms.service.IAuthorizationService;
import my.com.cmg.rms.service.IUserService;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class UserService implements IUserService {

  private final SecUserRepository userRepository;
  private final IAuthorizationService authorizationService;

  @Override
  public AuthUserDTO getAuthUserByUsername(String username) {
    SecUser user = userRepository.findActiveUserByUsername(username).orElse(null);
    if (user == null) {
      return null;
    }

    List<String> roles = authorizationService.getRoleListByUserId(user.getUsrId());
    boolean isAdmin = authorizationService.isUserAdmin(user.getUsrId());

    return new AuthUserDTO(
        user.getUsrId(),
        user.getUsername(),
        user.getUsrFullName(),
        user.getDivisionId(),
        user.getDivisionName(),
        roles,
        isAdmin);
  }

  @Override
  public SecUserDTO getUserByUsername(String username) {
    SecUser user = userRepository.findByUsername(username).orElse(null);
    if (user == null) {
      return null;
    }

    List<String> roles = authorizationService.getRoleListByUserId(user.getUsrId());

    return new SecUserDTO(
        user.getUsrId(),
        user.getUsername(),
        user.getUsrFullName(),
        user.getEmail(),
        user.getDivisionId(),
        user.getDivisionName(),
        user.getUsrStatus(),
        roles,
        user.getLastLoginDate(),
        user.getCreatedDate(),
        user.getUpdatedDate());
  }

  @Override
  public SecUserDTO getUserById(Long userId) {
    // Implementation similar to getUserByUsername
    return null; // Placeholder
  }

  @Override
  public List<SecUserDTO> getUsersByDivision(Long divisionId) {
    // Implementation untuk get users by division
    return List.of(); // Placeholder
  }

  @Override
  public SecUserDTO createUser(SecUserDTO userDTO) {
    // Implementation untuk create user
    return null; // Placeholder
  }

  @Override
  public SecUserDTO updateUser(Long userId, SecUserDTO userDTO) {
    // Implementation untuk update user
    return null; // Placeholder
  }

  @Override
  public void deleteUser(Long userId) {
    // Implementation untuk delete user
  }
}