package my.com.cmg.rms.model;

import java.time.LocalDateTime;
import java.time.LocalTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_frequency_schedules")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class FrequencySchedules extends BaseEntity {

  @Id
  @Column(name = "schedule_seqno", unique = true, nullable = false)
  @SequenceGenerator(name = "frequency_schedule_seq_no", sequenceName = "rm_frequency_schedules_seq", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "frequency_schedule_seq_no")
  private Long scheduleSeqno;

  @Column(name = "schedule_time")
  private LocalTime scheduleTime;

  @Column(name = "freq_seqno", nullable = false)
  private Long freqSeqno;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "freq_seqno", referencedColumnName = "freq_seqno", insertable = false, updatable = false)
  private Frequencies frequency;

  @Column(name = "regimen_day_seqno")
  private Long regimenDaySeqno;

  @Column(name = "iwp_sync_date")
  private LocalDateTime iwpSyncDate;

  @Column(name = "insuline_desc", length = 20)
  private String insulineDesc;
}