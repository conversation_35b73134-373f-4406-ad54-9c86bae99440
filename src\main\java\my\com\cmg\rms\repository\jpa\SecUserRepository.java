package my.com.cmg.rms.repository.jpa;

import java.util.List;
import java.util.Optional;
import my.com.cmg.rms.model.SecUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface SecUserRepository extends JpaRepository<SecUser, Long> {

  Optional<SecUser> findByUsername(String username);

  Optional<SecUser> findByEmail(String email);

  @Query("SELECT u FROM SecUser u WHERE u.username = :username AND u.activeFlag = 'A'")
  Optional<SecUser> findActiveUserByUsername(@Param("username") String username);

  @Query("SELECT u FROM SecUser u WHERE u.divisionId = :divisionId AND u.activeFlag = 'A'")
  List<SecUser> findUsersByDivision(@Param("divisionId") Long divisionId);
}