package my.com.cmg.rms.service;

import java.util.List;
import my.com.cmg.rms.dto.security.AuthUserDTO;
import my.com.cmg.rms.dto.security.SecUserDTO;

public interface IUserService {
  SecUserDTO getUserByUsername(String username);

  SecUserDTO getUserById(Long userId);

  AuthUserDTO getAuthUserByUsername(String username);

  List<SecUserDTO> getUsersByDivision(Long divisionId);

  SecUserDTO createUser(SecUserDTO userDTO);

  SecUserDTO updateUser(Long userId, SecUserDTO userDTO);

  void deleteUser(Long userId);
}