package my.com.cmg.rms.model;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_uoms")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class Uoms extends BaseEntity {

  @Id
  @Column(name = "uom_seqno", unique = true, nullable = false)
  @SequenceGenerator(name = "uom_seq_no", sequenceName = "rm_uoms_seq", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "uom_seq_no")
  private Long uomSeqno;

  @Column(name = "uom_abbreviation", length = 10, nullable = false)
  private String uomAbbreviation;

  @Column(name = "uom_desc", length = 500, nullable = false)
  private String uomDesc;

  @Column(name = "unit_type", length = 40)
  private String unitType;

  @Column(name = "uom_status", length = 1)
  private String uomStatus;

  @Column(name = "mims_uom_desc", length = 500)
  private String mimsUomDesc;

  @Column(name = "uom_local_desc", length = 500)
  private String uomLocalDesc;

  @Column(name = "iwp_sync_date")
  private LocalDateTime iwpSyncDate;
}