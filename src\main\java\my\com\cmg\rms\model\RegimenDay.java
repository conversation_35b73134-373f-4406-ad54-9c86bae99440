package my.com.cmg.rms.model;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_regimen_days")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RegimenDay extends BaseEntity {

  @Id
  @Column(name = "regimen_day_seqno", unique = true, nullable = false)
  @SequenceGenerator(name = "regimen_day_seq_no", sequenceName = "rm_regimen_days_seq", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "regimen_day_seq_no")
  private Long regimenDaySeqno;

  @Column(name = "freq_seqno")
  private Long freqSeqno;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "freq_seqno", referencedColumnName = "freq_seqno", insertable = false, updatable = false)
  private Frequencies frequency;

  @Column(name = "day_1", length = 1)
  private String day1;

  @Column(name = "day_2", length = 1)
  private String day2;

  @Column(name = "day_3", length = 1)
  private String day3;

  @Column(name = "day_4", length = 1)
  private String day4;

  @Column(name = "day_5", length = 1)
  private String day5;

  @Column(name = "day_6", length = 1)
  private String day6;

  @Column(name = "day_7", length = 1)
  private String day7;

  @Column(name = "day_8", length = 1)
  private String day8;

  @Column(name = "day_9", length = 1)
  private String day9;

  @Column(name = "day_10", length = 1)
  private String day10;

  @Column(name = "day_11", length = 1)
  private String day11;

  @Column(name = "day_12", length = 1)
  private String day12;

  @Column(name = "day_13", length = 1)
  private String day13;

  @Column(name = "day_14", length = 1)
  private String day14;

  @Column(name = "day_15", length = 1)
  private String day15;

  @Column(name = "day_16", length = 1)
  private String day16;

  @Column(name = "day_17", length = 1)
  private String day17;

  @Column(name = "day_18", length = 1)
  private String day18;

  @Column(name = "day_19", length = 1)
  private String day19;

  @Column(name = "day_20", length = 1)
  private String day20;

  @Column(name = "day_21", length = 1)
  private String day21;

  @Column(name = "day_22", length = 1)
  private String day22;

  @Column(name = "day_23", length = 1)
  private String day23;

  @Column(name = "day_24", length = 1)
  private String day24;

  @Column(name = "day_25", length = 1)
  private String day25;

  @Column(name = "day_26", length = 1)
  private String day26;

  @Column(name = "day_27", length = 1)
  private String day27;

  @Column(name = "day_28", length = 1)
  private String day28;

  @Column(name = "day_29", length = 1)
  private String day29;

  @Column(name = "day_30", length = 1)
  private String day30;

  @Column(name = "regimen_frequency", length = 20)
  private String regimenFrequency;

  @Column(name = "iwp_sync_date")
  private LocalDateTime iwpSyncDate;
}