package my.com.cmg.rms.model;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_dosage_forms")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class DosageForms extends BaseEntity {

  @Id
  @Column(name = "dosage_seqno", unique = true, nullable = false)
  @SequenceGenerator(name = "dosage_form_seq_no", sequenceName = "rm_dosage_forms_seq", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "dosage_form_seq_no")
  private Long dosageSeqno;

  @Column(name = "dosage_code", length = 15, nullable = false)
  private String dosageCode;

  @Column(name = "dosage_desc", length = 20, nullable = false)
  private String dosageDesc;

  @Column(name = "dosage_status", length = 1)
  private String dosageStatus;

  @Column(name = "iwp_sync_date")
  private LocalDateTime iwpSyncDate;
}