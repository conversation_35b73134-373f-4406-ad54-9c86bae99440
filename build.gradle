plugins {
	id 'org.springframework.boot' version '3.3.0'
	id 'java'
}

apply plugin: 'io.spring.dependency-management'

group = 'my.com.cmg.rms'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '17'

repositories {
	mavenCentral()
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'jakarta.persistence:jakarta.persistence-api:3.1.0'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-jooq'
	implementation 'org.springframework.boot:spring-boot-starter-aop'
	
	runtimeOnly 'org.postgresql:postgresql'
	compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok' 
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.projectlombok:lombok'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

test {
	useJUnitPlatform()
}

bootBuildImage {
  imageName="10.1.2.150:5000/rms"
}

dependencies {
  implementation 'org.springframework.boot:spring-boot-starter-security'
  implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
}

