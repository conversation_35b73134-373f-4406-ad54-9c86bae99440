package my.com.cmg.rms.model;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_indications")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class Indications extends BaseEntity {

  @Id
  @Column(name = "indication_seqno", unique = true, nullable = false)
  @SequenceGenerator(name = "indication_seq_no", sequenceName = "rm_indications_seq", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "indication_seq_no")
  private Long indicationSeqno;

  @Column(name = "code", length = 10, nullable = false)
  private String code;

  @Column(name = "fukkm_desc", length = 400, nullable = false)
  private String fukkmDesc;

  @Column(name = "indication_desc", length = 100, nullable = false)
  private String indicationDesc;

  @Column(name = "indication_local", length = 100, nullable = false)
  private String indicationLocal;

  @Column(name = "iwp_sync_date")
  private LocalDateTime iwpSyncDate;
}