package my.com.cmg.rms.service;

import java.util.List;
import my.com.cmg.rms.dto.RefCodesDTO;

public interface IUtilsService {
  List<RefCodesDTO> getRequestType();

  List<RefCodesDTO> getCategory();

  List<RefCodesDTO> getAssignment();

  List<RefCodesDTO> getStatus();

  List<RefCodesDTO> getSubCategory(String category);

  List<RefCodesDTO> getIntention();

  List<RefCodesDTO> getAllDosage();

  List<RefCodesDTO> getItemClass();

  List<RefCodesDTO> getItemSubClass();

  List<RefCodesDTO> getState();

  List<RefCodesDTO> getItemGroup();

  List<RefCodesDTO> getDrugSchedule();

  List<RefCodesDTO> getDrugType();

  List<RefCodesDTO> getSpecialOrderConfiguration();

  List<RefCodesDTO> getSku();

  List<RefCodesDTO> getPku();

  List<RefCodesDTO> getRadiopharmaceuticalItem();

  List<RefCodesDTO> getFacilityGroup();

  List<RefCodesDTO> getMinistry();

  List<RefCodesDTO> getFacilityCategory();

  List<RefCodesDTO> getFacilityType();

  List<RefCodesDTO> getCompanyStatus();

  List<RefCodesDTO> getItemDetail();

  List<RefCodesDTO> getDivision();
}
