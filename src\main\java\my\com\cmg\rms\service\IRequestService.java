package my.com.cmg.rms.service;

import java.util.List;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.dto.paging.PaginationResponseDTO;
import my.com.cmg.rms.dto.requestDetail.SaveRequestDTO;
import my.com.cmg.rms.dto.requestListing.RequestListDTO;
import my.com.cmg.rms.dto.requestListing.RequestListSearchDTO;
import my.com.cmg.rms.dto.viewDetail.ViewRequestDTO;

public interface IRequestService {

  Long save(SaveRequestDTO dto);

  List<RequestListDTO> getRequestList(RequestListSearchDTO requestDTO, PaginationRequestDTO pgDTO);

  PaginationResponseDTO getRequestListPages(RequestListSearchDTO requestDTO, Long size);

  ViewRequestDTO getViewRequest(Long requestHdrSeqno);

  void update(Long requestHdrSeqno, SaveRequestDTO dto);

  void confirmRequest(Long requestHdrSeqno);
}
