package my.com.cmg.rms.service.impl;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.model.SecUser;
import my.com.cmg.rms.repository.jooq.AuthorizationRepositoryJooq;
import my.com.cmg.rms.repository.jpa.SecUserRepository;
import my.com.cmg.rms.service.IAuthorizationService;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class AuthorizationService implements IAuthorizationService {

  private final AuthorizationRepositoryJooq authRepoJooq;
  private final SecUserRepository userRepository;

  @Override
  public List<String> getRoleListByUsername(String username) {
    log.info("Getting roles for username: {}", username);
    return authRepoJooq.getRolesByUsername(username);
  }

  @Override
  public List<String> getRoleListByUserId(Long userId) {
    log.info("Getting roles for userId: {}", userId);
    return authRepoJooq.getRolesByUserId(userId);
  }

  @Override
  public Long getUserDivisionId(Long userId) {
    SecUser user = userRepository.findById(userId).orElse(null);
    return user != null ? user.getDivisionId() : null;
  }

  @Override
  public boolean isUserAdmin(Long userId) {
    List<String> roles = getRoleListByUserId(userId);
    return roles.contains("ADMIN") || roles.contains("SYSTEM_ADMIN");
  }

  @Override
  public boolean canAccessDivision(Long userId, Long divisionId) {
    if (isUserAdmin(userId)) {
      return true; // Admin boleh akses semua division
    }

    Long userDivisionId = getUserDivisionId(userId);
    return userDivisionId != null && userDivisionId.equals(divisionId);
  }
}