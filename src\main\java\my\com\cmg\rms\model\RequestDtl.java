package my.com.cmg.rms.model;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.*;

@Entity
@Table(name = "rm_request_dtl")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RequestDtl extends BaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "request_dtl_seq")
  @SequenceGenerator(
      name = "request_dtl_seq",
      sequenceName = "rm_request_dtl_seq",
      allocationSize = 1)
  @Column(name = "request_dtl_seqno")
  private Long requestDtlSeqno;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "request_hdr_seqno", nullable = false)
  private RequestHdr requestHdr;

  @Column(name = "trans_seqno", nullable = false)
  private Long transSeqno;

  @Column(name = "trans_code", nullable = false, length = 50)
  private String transCode;

  @Column(name = "trans_name", nullable = false, length = 100)
  private String transName;

  @Column(name = "trans_type", nullable = false, length = 50)
  private String transType;

  @Column(name = "trans_details", nullable = false, length = 200)
  private String transDetails;

  @Column(name = "parameter1", length = 100)
  private String parameter1;

  @Column(name = "parameter2", length = 100)
  private String parameter2;

  @Column(name = "parameter3", precision = 8, scale = 4)
  private BigDecimal parameter3;

  @Column(name = "parameter4", precision = 8, scale = 4)
  private BigDecimal parameter4;

  @Column(name = "parameter5")
  private LocalDate parameter5;

  @Column(name = "created_date", nullable = false)
  private LocalDateTime createdDate;

  @Column(name = "updated_date", nullable = false)
  private LocalDateTime updatedDate;

  @Column(name = "active_flag", length = 1)
  private Character activeFlag;
}
