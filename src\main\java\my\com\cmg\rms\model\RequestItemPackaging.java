package my.com.cmg.rms.model;

import jakarta.persistence.*;
import java.math.BigDecimal;
import lombok.*;

@Entity
@Table(name = "rm_request_item_packaging")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RequestItemPackaging extends BaseEntity {

  @Id
  @Column(name = "item_packaging_req_seqno", unique = true, nullable = false)
  @SequenceGenerator(
      name = "request_item_packaging_seq_no",
      sequenceName = "rm_request_item_packaging_seq",
      allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "request_item_packaging_seq_no")
  private Long itemPackagingReqSeqno;

  @Column(name = "item_packaging_req_code", length = 20, nullable = false)
  private String itemPackagingReqCode;

  @Column(name = "item_packaging_req_name", length = 200)
  private String itemPackagingReqName;

  @Column(name = "item_seqno", nullable = false)
  private Long itemSeqno;

  @Column(name = "item_name", length = 250)
  private String itemName;

  @Column(name = "item_code", length = 20, nullable = false)
  private String itemCode;

  @Column(name = "item_packaging_name", length = 200)
  private String itemPackagingName;

  @Column(name = "sku_seqno", nullable = false)
  private Long skuSeqno;

  @Column(name = "sku_abbr", length = 10, nullable = false)
  private String skuAbbr;

  @Column(name = "pku_seqno", nullable = false)
  private Long pkuSeqno;

  @Column(name = "pku_abbr", length = 10, nullable = false)
  private String pkuAbbr;

  @Column(name = "conversion_factor", precision = 10, scale = 1)
  private BigDecimal conversionFactor;

  @Column(name = "packaging_desc", length = 50)
  private String packagingDesc;

  @Column(name = "product_list", length = 100)
  private String productList;

  @Column(name = "product_seqno", nullable = false)
  private Long productSeqno;

  @Column(name = "product_code", length = 15)
  private String productCode;

  @Column(name = "product_desc", length = 20)
  private String productDesc;

  @Column(name = "product_name", length = 100)
  private String productName;

  @Column(name = "inovator_type_yn", length = 1)
  private String inovatorTypeYn = "N";

  @Column(name = "generic_type_yn", length = 1)
  private String genericTypeYn = "N";

  @Column(name = "manufactured_name", length = 100)
  private String manufacturedName;

  @Column(name = "manufactured_address", length = 200)
  private String manufacturedAddress;

  @Column(name = "importer_name", length = 100)
  private String importerName;

  @Column(name = "importer_address", length = 200)
  private String importerAddress;

  @Column(name = "gtin_no", length = 20)
  private String gtinNo;

  @Column(name = "mda_no", length = 20)
  private String mdaNo;
}
