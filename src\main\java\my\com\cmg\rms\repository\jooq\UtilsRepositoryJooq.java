package my.com.cmg.rms.repository.jooq;

import static org.jooq.impl.DSL.*;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.constant.RmsConstant;
import my.com.cmg.rms.dto.RefCodesDTO;
import my.com.cmg.rms.utils.LogUtil;
import my.com.cmg.rms.utils.TableUtil;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record2;
import org.jooq.Select;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
@AllArgsConstructor
public class UtilsRepositoryJooq {
  private final DSLContext dsl;

  public List<RefCodesDTO> getRefCodesByDomain(String domain) {
    Condition condition = noCondition();
    condition = condition.and(field("ref.rc_domain").eq(domain));

    Field<String> rcDesc = field("ref.rc_desc", String.class);
    Field<String> rcValue = field("ref.rc_value", String.class);

    Select<Record2<String, String>> query = dsl.select(rcValue, rcDesc)
        .from(TableUtil.table(TableUtil.RM_REF_CODES, "ref")).where(condition);

    log.info(LogUtil.QUERY, query);
    List<RefCodesDTO> result = query.fetchInto(RefCodesDTO.class);
    return result;
  }

  public List<RefCodesDTO> getRefCodesByDomainAndParent(String domain, String parent) {
    Condition condition = noCondition();
    condition = condition.and(field("ref.rc_domain").eq(domain));
    condition = condition.and(field("ref.rc_remarks").eq(parent));

    Field<String> rcDesc = field("ref.rc_desc", String.class);
    Field<String> rcValue = field("ref.rc_value", String.class);

    Select<Record2<String, String>> query = dsl.select(rcValue, rcDesc)
        .from(TableUtil.table(TableUtil.RM_REF_CODES, "ref")).where(condition);

    log.info(LogUtil.QUERY, query);
    List<RefCodesDTO> result = query.fetchInto(RefCodesDTO.class);
    return result;
  }

  public List<RefCodesDTO> getActiveRefCodes(String domain, String sortDirection) {
    Condition condition = noCondition();
    condition = condition.and(field("ref.rc_domain").eq(domain));
    condition = condition.and(field("ref.active_flag").eq(RmsConstant.ACTIVE_FLAG_TRUE.toString()));

    Field<String> rcDesc = field("ref.rc_desc", String.class);
    Field<String> rcValue = field("ref.rc_value", String.class);

    Select<Record2<String, String>> query = dsl.select(rcValue, rcDesc)
        .from(TableUtil.table(TableUtil.RM_REF_CODES, "ref")).where(condition)
        .orderBy(sortDirection.equals("asc") ? rcDesc.asc() : rcDesc.desc());

    log.info(LogUtil.QUERY, query);

    List<RefCodesDTO> result = query.fetchInto(RefCodesDTO.class);
    return result;
  }

  public RefCodesDTO getRefCodesByValue(String domain, String value) {
    Condition condition = noCondition();
    condition = condition.and(field("ref.rc_domain").eq(domain));
    condition = condition.and(field("ref.rc_value").eq(value));

    Field<String> rcDesc = field("ref.rc_desc", String.class);
    Field<String> rcValue = field("ref.rc_value", String.class);

    Select<Record2<String, String>> query = dsl.select(rcValue, rcDesc)
        .from(TableUtil.table(TableUtil.RM_REF_CODES, "ref")).where(condition);

    log.info(LogUtil.QUERY, query);
    RefCodesDTO result = query.fetchOneInto(RefCodesDTO.class);
    return result;
  }

  public RefCodesDTO getRefCodesByDesc(String domain, String desc) {
    Condition condition = noCondition();
    condition = condition.and(field("ref.rc_domain").eq(domain));
    condition = condition.and(field("ref.rc_desc").eq(desc));

    Field<String> rcDesc = field("ref.rc_desc", String.class);
    Field<String> rcValue = field("ref.rc_value", String.class);

    Select<Record2<String, String>> query = dsl.select(rcValue, rcDesc)
        .from(TableUtil.table(TableUtil.RM_REF_CODES, "ref")).where(condition);

    log.info(LogUtil.QUERY, query);
    RefCodesDTO result = query.fetchOneInto(RefCodesDTO.class);
    return result;
  }

  // dosage form dropdown
  public List<RefCodesDTO> getAllDosage() {
    Condition condition = noCondition();

    Field<Long> dosageSeqno = field("dosage_seqno", Long.class);
    Field<String> dosageDesc = field("dosage_desc", String.class);

    Select<Record2<Long, String>> query = dsl.select(dosageSeqno, dosageDesc)
        .from(TableUtil.table(TableUtil.RM_DOSAGE_FORMS, "ref")).where(condition);

    List<RefCodesDTO> result = query.fetchInto(RefCodesDTO.class);

    return result;
  }

  // item class dropdown
  public List<RefCodesDTO> getItemClass() {
    Condition condition = noCondition();

    Field<Long> itmCatSeqno = field("itm_cat_seqno", Long.class);
    Field<String> itmCatDesc = field("cat_desc", String.class);

    Select<Record2<Long, String>> query = dsl.select(itmCatSeqno, itmCatDesc)
        .from(TableUtil.table(TableUtil.RM_ITEM_CLASS, "ref")).where(condition);

    List<RefCodesDTO> result = query.fetchInto(RefCodesDTO.class);

    return result;
  }

  // item Subclass dropdown
  public List<RefCodesDTO> getItemSubClass() {
    Condition condition = noCondition();

    Field<Long> itmSubgroupSeqno = field("itm_subgroup_seqno", Long.class);
    Field<String> subgroupDesc = field("subgroup_desc", String.class);

    Select<Record2<Long, String>> query = dsl.select(itmSubgroupSeqno, subgroupDesc)
        .from(TableUtil.table(TableUtil.RM_ITEM_SUBCLASS, "ref")).where(condition);

    List<RefCodesDTO> result = query.fetchInto(RefCodesDTO.class);

    return result;
  }

  // sku dropdown
  public List<RefCodesDTO> getSku() {
    Condition condition = noCondition();

    Field<Long> uomSeqno = field("uom_seqno", Long.class);
    Field<String> uomDesc = field("uom_desc", String.class);

    Select<Record2<Long, String>> query = dsl.select(uomSeqno, uomDesc).from(TableUtil.table(TableUtil.RM_UOMS, "ref"))
        .where(condition);

    List<RefCodesDTO> result = query.fetchInto(RefCodesDTO.class);

    return result;
  }

  // pku dropdown
  public List<RefCodesDTO> getPku() {
    Condition condition = noCondition();

    Field<Long> uomSeqno = field("uom_seqno", Long.class);
    Field<String> uomDesc = field("uom_desc", String.class);

    Select<Record2<Long, String>> query = dsl.select(uomSeqno, uomDesc).from(TableUtil.table(TableUtil.RM_UOMS, "ref"))
        .where(condition);

    List<RefCodesDTO> result = query.fetchInto(RefCodesDTO.class);

    return result;
  }

}