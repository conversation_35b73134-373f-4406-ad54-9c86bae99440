package my.com.cmg.rms.model;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_generic_names")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class GenericNames extends BaseEntity {

  @Id
  @Column(name = "generic_seqno", unique = true, nullable = false)
  @SequenceGenerator(name = "generic_name_seq_no", sequenceName = "rm_generic_names_seq", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "generic_name_seq_no")
  private Long genericSeqno;

  @Column(name = "generic_code", length = 20, nullable = false)
  private String genericCode;

  @Column(name = "generic_desc", length = 350)
  private String genericDesc;

  @Column(name = "generic_status", length = 1)
  private String genericStatus;

  @Column(name = "atc_code", length = 350)
  private String atcCode;

  @Column(name = "iwp_sync_date")
  private LocalDateTime iwpSyncDate;
}