package my.com.cmg.rms.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_frequencies")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class Frequencies extends BaseEntity {

  @Id
  @Column(name = "freq_seqno", unique = true, nullable = false)
  @SequenceGenerator(name = "frequency_seq_no", sequenceName = "rm_frequencies_seq", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "frequency_seq_no")
  private Long freqSeqno;

  @Column(name = "freq_code", length = 15, nullable = false)
  private String freqCode;

  @Column(name = "freq_desc", length = 250, nullable = false)
  private String freqDesc;

  @Column(name = "freq_local_desc", length = 250)
  private String freqLocalDesc;

  @Column(name = "type", length = 20)
  private String type;

  @Column(name = "noofadmin")
  private BigDecimal noOfAdmin;

  @Column(name = "noofadmin_duration_type", length = 10)
  private String noOfAdminDurationType;

  @Column(name = "interval_value")
  private BigDecimal intervalValue;

  @Column(name = "interval_duration_type", length = 10)
  private String intervalDurationType;

  @Column(name = "scheduled_yn", length = 1)
  private String scheduledYn;

  @Column(name = "days", length = 60)
  private String days;

  @Column(name = "freq_status", length = 1)
  private String freqStatus;

  @Column(name = "freq_english_desc", length = 250)
  private String freqEnglishDesc;

  @Column(name = "sunday_yn", length = 1)
  private String sundayYn;

  @Column(name = "monday_yn", length = 1)
  private String mondayYn;

  @Column(name = "tuesday_yn", length = 1)
  private String tuesdayYn;

  @Column(name = "wednesday_yn", length = 1)
  private String wednesdayYn;

  @Column(name = "thursday_yn", length = 1)
  private String thursdayYn;

  @Column(name = "friday_yn", length = 1)
  private String fridayYn;

  @Column(name = "saturday_yn", length = 1)
  private String saturdayYn;

  @Column(name = "mims_freq_value", length = 50)
  private String mimsFreqValue;

  @Column(name = "mims_freq_period", length = 50)
  private String mimsFreqPeriod;

  @Column(name = "mar_type", length = 10)
  private String marType;

  @Column(name = "iwp_sync_date")
  private LocalDateTime iwpSyncDate;

  @Column(name = "insuline_label", length = 1, nullable = false)
  private String insulineLabel;

  @Column(name = "hemodialysis_yn", length = 1)
  private String hemodialysisYn;

  @Column(name = "is_hd", length = 1)
  private String isHd;

  @Column(name = "hemodialysis_freq_code", length = 20)
  private String hemodialysisFreqCode;

  @Column(name = "freq_per_week")
  private Integer freqPerWeek;
}