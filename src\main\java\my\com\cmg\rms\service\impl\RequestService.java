package my.com.cmg.rms.service.impl;

import static my.com.cmg.rms.constant.RmsConstant.*;

import jakarta.transaction.Transactional;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.RequestDtlDTO;
import my.com.cmg.rms.dto.RequestHeaderDTO;
import my.com.cmg.rms.dto.RequestHeaderDtlDTO;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.dto.paging.PaginationResponseDTO;
import my.com.cmg.rms.dto.requestDetail.FacilityMasterDetailDTO;
import my.com.cmg.rms.dto.requestDetail.ItemMasterDetailDTO;
import my.com.cmg.rms.dto.requestDetail.ItemPackagingDetailDTO;
import my.com.cmg.rms.dto.requestDetail.SaveRequestDTO;
import my.com.cmg.rms.dto.requestDetail.SupplierMasterDetailDTO;
import my.com.cmg.rms.dto.requestListing.RequestListDTO;
import my.com.cmg.rms.dto.requestListing.RequestListSearchDTO;
import my.com.cmg.rms.dto.viewDetail.FacilityMasterRequestDTO;
import my.com.cmg.rms.dto.viewDetail.ItemMasterRequestDTO;
import my.com.cmg.rms.dto.viewDetail.ItemPackagingRequestDTO;
import my.com.cmg.rms.dto.viewDetail.SupplierMasterRequestDTO;
import my.com.cmg.rms.dto.viewDetail.ViewRequestDTO;
import my.com.cmg.rms.exception.ExceptionCode;
import my.com.cmg.rms.exception.RmsException;
import my.com.cmg.rms.mapper.RequestMapper;
import my.com.cmg.rms.model.RequestDtl;
import my.com.cmg.rms.model.RequestFacility;
import my.com.cmg.rms.model.RequestHdr;
import my.com.cmg.rms.model.RequestItemMaster;
import my.com.cmg.rms.model.RequestItemPackaging;
import my.com.cmg.rms.model.RequestSupplier;
import my.com.cmg.rms.repository.jooq.RequestRepositoryJooq;
import my.com.cmg.rms.repository.jpa.RequestDtlRepository;
import my.com.cmg.rms.repository.jpa.RequestFacilityRepository;
import my.com.cmg.rms.repository.jpa.RequestHdrRepository;
import my.com.cmg.rms.repository.jpa.RequestItemMasterRepository;
import my.com.cmg.rms.repository.jpa.RequestItemPackagingRepository;
import my.com.cmg.rms.repository.jpa.RequestSupplierRepository;
import my.com.cmg.rms.service.IRequestService;
import my.com.cmg.rms.utils.PaginationUtil;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class RequestService implements IRequestService {

  private final RequestRepositoryJooq requestRepositoryJooq;
  private final RequestHdrRepository requestHdrRepository;
  private final RequestDtlRepository requestDtlRepository;
  private final RequestSupplierRepository requestSupplierRepository;
  private final RequestFacilityRepository requestFacilityRepository;
  private final RequestItemMasterRepository requestItemMasterRepository;
  private final RequestItemPackagingRepository requestItemPackagingRepository;

  @Override
  @Transactional
  public Long save(SaveRequestDTO dto) {
    RequestHdr hdr = saveRequestHeader(dto.requestHeaderDtl());
    String requestType = dto.requestHeaderDtl().requestType();
    String subCategory = dto.requestHeaderDtl().subCategory();

    boolean isNew = DATA_REQUEST_TYPE_NEW.toString().equalsIgnoreCase(requestType);
    boolean isUpdate = DATA_REQUEST_TYPE_UPDATE.toString().equalsIgnoreCase(requestType);

    if (isUpdate && dto.requestDtl() != null) {
      saveRequestDetail(dto.requestDtl(), hdr);
      if (DATA_REQUEST_SUB_CATEGORY_NEW_SUPPLIER_MASTER.equalsIgnoreCase(subCategory)) {
        saveSupplier(dto.supplierMaster(), hdr);
      } else if (DATA_REQUEST_SUB_CATEGORY_NEW_ITEM_MASTER.equalsIgnoreCase(subCategory)) {
        saveItem(dto.itemMaster(), hdr);
      } else if (DATA_REQUEST_SUB_CATEGORY_NEW_ITEM_PACKAGING.equalsIgnoreCase(subCategory)) {
        savePackaging(dto.itemPackaging(), hdr);
      }
    }
    if (isNew) {
      if (dto.requestDtl() != null) {
        saveRequestDetail(dto.requestDtl(), hdr);
      }
      if (DATA_REQUEST_SUB_CATEGORY_NEW_SUPPLIER_MASTER.equalsIgnoreCase(subCategory)) {
        saveSupplier(dto.supplierMaster(), hdr);
      } else if (DATA_REQUEST_SUB_CATEGORY_NEW_ITEM_MASTER.equalsIgnoreCase(subCategory)) {
        saveItem(dto.itemMaster(), hdr);
      } else if (DATA_REQUEST_SUB_CATEGORY_NEW_ITEM_PACKAGING.equalsIgnoreCase(subCategory)) {
        savePackaging(dto.itemPackaging(), hdr);
      } else if (DATA_REQUEST_SUB_CATEGORY_NEW_FACILITY_MASTER.equalsIgnoreCase(subCategory)) {
        saveFacility(dto.facilityMaster(), hdr);
      }
    }
    return hdr.getRequestHdrSeqno();
  }

  @Override
  @Transactional
  public void update(Long requestHdrSeqno, SaveRequestDTO dto) {
    RequestHdr existingHdr =
        requestHdrRepository
            .findById(requestHdrSeqno)
            .orElseThrow(() -> new RmsException(null, null));

    RequestHeaderDtlDTO headerDTO = dto.requestHeaderDtl();
    existingHdr.setTitle(headerDTO.title());
    existingHdr.setReference(headerDTO.reference());
    existingHdr.setIntention(headerDTO.intention());
    existingHdr.setReason(headerDTO.reason());
    existingHdr.setUpdatedBy(1L);
    existingHdr.setStatus("DRAFT");
    existingHdr.setUpdatedDate(LocalDateTime.now());
    requestHdrRepository.save(existingHdr);

    List<RequestDtl> dtls = requestDtlRepository.findByRequestHdrRequestHdrSeqno(requestHdrSeqno);

    if (!dtls.isEmpty() && dto.requestDtl() != null) {
      RequestDtl existingDtl = dtls.get(0);
      RequestDtlDTO dtlDTO = dto.requestDtl();
      existingDtl.setTransDetails(dtlDTO.transDetails());
      existingDtl.setUpdatedBy(1L);
      existingDtl.setUpdatedDate(LocalDateTime.now());
      requestDtlRepository.save(existingDtl);
    }
  }

  @Override
  @Transactional
  public void confirmRequest(Long requestHdrSeqno) {
    RequestHdr hdr =
        requestHdrRepository
            .findById(requestHdrSeqno)
            .orElseThrow(() -> new RmsException(ExceptionCode.ERROR, "Request not found"));

    if (!"DRAFT".equalsIgnoreCase(hdr.getStatus())) {
      throw new RmsException(ExceptionCode.ERROR, "Only draft requests can be confirmed");
    }

    hdr.setStatus("PENDING");
    hdr.setUpdatedBy(1L);
    hdr.setUpdatedDate(LocalDateTime.now());

    requestHdrRepository.save(hdr);
  }

  private RequestHdr saveRequestHeader(RequestHeaderDtlDTO headerDtlDTO) {
    RequestHdr entity = new RequestHdr();
    entity.setRequestNo(headerDtlDTO.requestNo());
    entity.setRequestType(headerDtlDTO.requestType());
    entity.setCategory(headerDtlDTO.category());
    entity.setSubCategory(headerDtlDTO.subCategory());
    entity.setTitle(headerDtlDTO.title());
    entity.setReference(headerDtlDTO.reference());
    entity.setIntention(headerDtlDTO.intention());
    entity.setReason(headerDtlDTO.reason());

    entity.setCreatedBy(1L);
    entity.setUpdatedBy(1L);
    entity.setStatus("DRAFT");
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setFacilitySeqno(1L);
    entity.setRequestedBySeqno(1L);
    entity.setRequestedDate(LocalDateTime.now());
    entity.setActiveFlag('A');

    return requestHdrRepository.save(entity);
  }

  private void saveRequestDetail(RequestDtlDTO dtlDTO, RequestHdr hdr) {
    RequestDtl entity = new RequestDtl();
    entity.setRequestHdr(hdr);
    entity.setTransSeqno(dtlDTO.transSeqno());
    entity.setTransCode(dtlDTO.transCode());
    entity.setTransName(dtlDTO.transName());
    entity.setTransType(dtlDTO.transType());
    entity.setTransDetails(dtlDTO.transDetails());
    entity.setCreatedBy(1L);
    entity.setUpdatedBy(1L);
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setActiveFlag('A');
    requestDtlRepository.save(entity);
  }

  private void saveSupplier(SupplierMasterDetailDTO dto, RequestHdr hdr) {
    RequestSupplier entity = new RequestSupplier();
    entity.setSupplierReqCode(dto.supplierReqCode());
    entity.setSupplierReqName(dto.supplierReqName());
    entity.setSupplierReqType(dto.supplierReqType());
    entity.setCompanyRegNo(dto.companyRegNo());
    if (dto.regExpiryDate() != null) {
      entity.setRegExpiryDate(dto.regExpiryDate().atStartOfDay());
    }
    entity.setTrsRegNo(dto.trsRegNo());
    entity.setCompanyStatus(dto.companyStatus());
    entity.setCompanyStatusDesc(dto.companyStatus());
    entity.setAddress1(dto.address1());
    entity.setAddress2(dto.address2());
    entity.setAddress3(dto.address3());
    entity.setCity(dto.city());
    entity.setState(dto.state());
    entity.setPostcode(dto.postcode());
    entity.setCountry(dto.country());
    entity.setMobilePhone(dto.mobilePhone());
    entity.setEmail(dto.email());
    entity.setContactPerson(dto.contactPerson());
    entity.setContactNo(dto.contactNo());
    entity.setCreatedBy(1L);
    entity.setUpdatedBy(1L);
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setActiveFlag('A');
    requestSupplierRepository.save(entity);
  }

  private void saveItem(ItemMasterDetailDTO dto, RequestHdr hdr) {
    RequestItemMaster entity = new RequestItemMaster();

    entity.setItemReqCode(dto.itemReqCode());
    entity.setItemGroupCode(dto.itemGroupCode());
    entity.setGenericNameSeqno(dto.genericNameSeqno());
    entity.setOtherActiveIngredient(dto.otherActiveIngredient());
    entity.setStrength(new BigDecimal(dto.strength()));
    entity.setDosageSeqno(dto.dosageSeqno());
    entity.setDosageCode(dto.dosageCode());
    entity.setItemName(dto.itemName());
    entity.setItemCategorySeqno(dto.itemCatSeqno());
    entity.setItemCategoryCode(dto.itemCategoryCode());
    entity.setItemSubgroupSeqno(dto.itemSubgroupSeqno());
    entity.setItemSubgroupCode(dto.itemSubgroupCode());
    entity.setRpItemTypeCode(dto.rpItemTypeCode());
    entity.setRpItemTypeDesc(dto.rpItemTypeDesc());
    entity.setItemPackagingSeqno(dto.itemPackagingSeqno());
    entity.setItemPackagingCode(dto.itemPackagingCode());
    entity.setItemPackagingName(dto.itemPackagingName());
    entity.setSkuSeqno(dto.skuSeqno());
    entity.setSkuAbbr(dto.skuAbbr());
    entity.setPkuSeqno(dto.pkuSeqno());
    entity.setPkuAbbr(dto.pkuAbbr());
    entity.setMdcNo(dto.mdcNo());
    entity.setProductSeqno(dto.productSeqno());
    entity.setProductName(dto.productName());
    entity.setManufacturedName(dto.manufacturedName());
    entity.setImporterName(dto.importerName());
    entity.setManufacturedAddress(dto.manufacturedAddress());
    entity.setImporterAddress(dto.importerAddress());
    entity.setGtinNo(dto.gtinNo());
    entity.setMdaNo(dto.mdaNo());
    entity.setConversionFactorNum((dto.conversionFactorNum()));
    entity.setPackagingDesc(dto.packagingDesc());

    entity.setCreatedBy(1L);
    entity.setUpdatedBy(1L);
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setActiveFlag('A');
    requestItemMasterRepository.save(entity);
  }

  private void savePackaging(ItemPackagingDetailDTO dto, RequestHdr hdr) {
    RequestItemPackaging entity = new RequestItemPackaging();
    entity.setItemSeqno(dto.itemSeqno());
    entity.setItemName(dto.itemName());
    entity.setItemCode(dto.itemCode());
    entity.setItemPackagingReqCode(dto.itemPackagingReqCode());
    entity.setItemPackagingName(dto.itemPackagingName());
    entity.setSkuSeqno(dto.skuSeqno());
    entity.setSkuAbbr(dto.skuAbbr());
    entity.setPkuSeqno(dto.pkuSeqno());
    entity.setPkuAbbr(dto.pkuAbbr());
    entity.setConversionFactor(new BigDecimal(dto.conversionFactor()));
    entity.setPackagingDesc(dto.packagingDesc());
    entity.setProductList(dto.productList());
    entity.setProductSeqno(dto.productSeqno());
    entity.setProductName(dto.productName());
    entity.setManufacturedName(dto.manufacturedName());
    entity.setImporterName(dto.importerName());
    entity.setManufacturedAddress(dto.manufacturedAddress());
    entity.setImporterAddress(dto.importerAddress());
    entity.setGtinNo(dto.gtinNo());
    entity.setMdaNo(dto.mdaNo());

    entity.setCreatedBy(1L);
    entity.setUpdatedBy(1L);
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setActiveFlag('A');
    requestItemPackagingRepository.save(entity);
  }

  private void saveFacility(FacilityMasterDetailDTO dto, RequestHdr hdr) {
    RequestFacility entity = new RequestFacility();
    entity.setFacilityReqName(dto.facilityReqName());
    entity.setFacilityReqGroup(dto.facilityReqGroup());
    entity.setMinistry(dto.ministry());
    entity.setFacilityReqCategory(dto.facilityReqCategory());
    entity.setFacilityReqType(dto.facilityReqType());

    entity.setAddress1(dto.address1());
    entity.setAddress2(dto.address2());
    entity.setAddress3(dto.address3());
    entity.setCity(dto.city());
    entity.setState(dto.state());
    entity.setPostcode(dto.postcode());
    entity.setCountry(dto.country());

    entity.setMobilePhone(dto.mobilePhone());
    entity.setEmail(dto.email());
    entity.setContactPerson(dto.contactPerson());
    entity.setContactNo(dto.contactNo());
    entity.setCreatedBy(1L);
    entity.setUpdatedBy(1L);
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setActiveFlag('A');
    requestFacilityRepository.save(entity);
  }

  @Override
  public List<RequestListDTO> getRequestList(
      RequestListSearchDTO requestDTO, PaginationRequestDTO pgDTO) {
    PaginationRequestDTO pg = PaginationUtil.pageSorting(pgDTO, new RequestMapper(), true);
    return requestRepositoryJooq.getRequestList(requestDTO, pg);
  }

  @Override
  public PaginationResponseDTO getRequestListPages(RequestListSearchDTO requestDTO, Long size) {
    Long pgSize = requestRepositoryJooq.getRequestListCount(requestDTO);
    return PaginationUtil.pagination(size, pgSize);
  }

  @Override
  public ViewRequestDTO getViewRequest(Long requestHdrSeqno) {
    RequestHeaderDTO header = requestRepositoryJooq.getRequestHeader(requestHdrSeqno);
    if (header == null) return null;
    RequestDtlDTO[] details = header.requestDetails();
    if (details == null || details.length == 0)
      return new ViewRequestDTO(header, null, null, null, null, null);
    Long transSeqno = details[0].transSeqno();
    String transType = details[0].transType();

    ItemMasterRequestDTO item = null;
    ItemPackagingRequestDTO packaging = null;
    FacilityMasterRequestDTO facility = null;
    SupplierMasterRequestDTO supplier = null;
    if ("IM".equalsIgnoreCase(transType)) {
      item = requestRepositoryJooq.getItemMasterRequest(transSeqno);
    } else if ("IP".equalsIgnoreCase(transType)) {
      packaging = requestRepositoryJooq.getItemPackagingRequest(transSeqno);
    } else if ("FM".equalsIgnoreCase(transType)) {
      facility = requestRepositoryJooq.getFacilityRequest(transSeqno);
    } else if ("SM".equalsIgnoreCase(transType)) {
      supplier = requestRepositoryJooq.getSupplierRequest(transSeqno);
    }

    return new ViewRequestDTO(header, details, item, packaging, facility, supplier);
  }
}
