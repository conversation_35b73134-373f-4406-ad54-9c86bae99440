package my.com.cmg.rms.mapper;

public class RequestMapper extends ColumnMapper {

  public RequestMapper() {
    COLUMN_MAP.put("requestNo", "requestNo");
    COLUMN_MAP.put("facilitySeqno", "facilitySeqno");
    COLUMN_MAP.put("requestType", "requestType");
    COLUMN_MAP.put("title", "title");
    COLUMN_MAP.put("assignedTo", "assignedTo");
    COLUMN_MAP.put("category", "category");
    COLUMN_MAP.put("requestDateFrom", "requestDateFrom");
    COLUMN_MAP.put("requestDateTo", "requestDateTo");
    COLUMN_MAP.put("status", "status");
  }
}
