package my.com.cmg.rms.security;

import lombok.AllArgsConstructor;
import my.com.cmg.rms.service.IAuthorizationService;
import my.com.cmg.rms.service.IUserService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
@AllArgsConstructor
public class SecurityConfig {

  private final IUserService userService;
  private final IAuthorizationService authorizationService;

  @Bean
  public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
    http.authorizeHttpRequests(
            authorize ->
                authorize
                    .requestMatchers("/api/v1/rms/auth/**")
                    .permitAll()
                    .requestMatchers("/api/v1/rms/public/**")
                    .permitAll()
                    .anyRequest()
                    .authenticated())
        .oauth2ResourceServer(
            oauth2 ->
                oauth2.jwt(
                    jwt ->
                        jwt.jwtAuthenticationConverter(
                            new CustomJwtConverter(userService, authorizationService))))
        .csrf(csrf -> csrf.disable());

    return http.build();
  }
}