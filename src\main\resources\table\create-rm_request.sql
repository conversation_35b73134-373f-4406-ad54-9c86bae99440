--Create sequence header request (rm_request_hdr_seq)

CREATE SEQUENCE rm_request_hdr_seq
    INCREMENT BY 1
    MINVALUE 1
    START WITH 1
    CACHE 1
    NO CYCLE;

--Create table header request (rm_request_hdr)

CREATE TABLE phisprod.rm_request_hdr (
    request_hdr_seqno      BIGINT PRIMARY KEY,
    request_no             VARCHAR(10) NOT NULL UNIQUE,
    request_type           VARCHAR(1) NOT NULL,
    request_type_desc      VARCHAR(20),                     -- denormalized
    category               VARCHAR(10) NOT NULL,
    category_desc          VARCHAR(50),                     -- denormalized
    sub_category           VARCHAR(10) NOT NULL,
    sub_category_desc      VARCHAR(50),                     -- denormalized
    title                  VARCHAR(100) NOT NULL,
    reference              VARCHAR(200),
    intention              VARCHAR(3),
    intention_desc         VARCHAR(50),                     -- denormalized
    reason                 VARCHAR(200) NOT NULL,
    facility_seqno         BIGINT NOT NULL,
    facility_name          VARCHAR(100),                    -- denormalized
    requested_by_seqno     BIGINT NOT NULL,
    requested_by_name      VA<PERSON>HA<PERSON>(100),                    -- denormalized
    requested_date         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    assigned_to            VARCHAR(20),
    assigned_to_desc       VARCHAR(100),                    -- denormalized
    assigned_from          VARCHAR(20),
    assigned_from_desc     VARCHAR(100),                    -- denormalized
    status                 VARCHAR(10),
    status_desc            VARCHAR(50),                     -- denormalized
    reject_reason          VARCHAR(200),
    -- default fields
    created_date           TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_date           TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    parameter1             VARCHAR(100),
    parameter2             VARCHAR(100),
    parameter3             NUMERIC(8, 4),
    parameter4             NUMERIC(8, 4),
    parameter5             DATE,
    active_flag            BPCHAR(1) DEFAULT 'A',
    created_by             BIGINT NOT NULL,
    updated_by             BIGINT NOT NULL
);

--Create table items request (rm_request_items)

DROP TABLE IF EXISTS phisprod.rm_request_items CASCADE;

CREATE TABLE rm_request_items
(
   item_req_seqno           bigint,
   item_req_code            varchar(20)     NOT NULL,
   item_req_desc            varchar(100),
   item_group_code          varchar(20)     NOT NULL,
   item_group_desc          varchar(100),
   generic_name_seqno       bigint,
   generic_name_code        varchar(20),
   generic_name_desc        varchar(100),
   other_active_ingredient  varchar(100),
   strength                 numeric(15,4)   DEFAULT 0,
   dosage_seqno             bigint,
   dosage_code              varchar(15),
   dosage_desc              varchar(20),
   item_name                varchar(250),
   itm_cat_seqno            bigint          NOT NULL,
   itm_cat_code             varchar(6)      NOT NULL,
   cat_desc                 varchar(50),
   itm_subgroup_seqno       bigint          NOT NULL,
   itm_subgroup_code        varchar(20)     NOT NULL,
   subgroup_desc            varchar(100),
   freq_seqno               bigint,
   freq_code                varchar(15),
   freq_desc                varchar(250),
   administration_route     varchar(100),
   drug_indication          varchar(100),
   rp_item_type_code        varchar(15),
   rp_item_type_desc        varchar(50)     NOT NULL,
   manufacturing_config     varchar(100),
   item_packaging_seqno     bigint          NOT NULL,
   item_packaging_code      varchar(20)     NOT NULL,
   item_packaging_name      varchar(200),
   sku_seqno                bigint          NOT NULL,
   sku_abbr                 varchar(10)     NOT NULL,
   pku_seqno                bigint          NOT NULL,
   pku_abbr                 varchar(10)     NOT NULL,
   conversion_factor_num    numeric(15,4)   DEFAULT 0,
   packaging_desc           varchar(100),
   mdc_no                   varchar(200),
   product_seqno            bigint          NOT NULL,
   product_code             varchar(15),
   product_desc             varchar(20),
   product_name             varchar(100),
   inovator_type_yn         char(1)         DEFAULT 'N'::bpchar,
   generic_type_yn          char(1)         DEFAULT 'N'::bpchar,
   manufactured_name        varchar(100),
   manufactured_address     varchar(200),
   importer_name            varchar(100),
   importer_address         varchar(200),
   gtin_no                  varchar(20),
   mda_no                   varchar(20)
);


--Create table facility request (rm_request_facility)

DROP TABLE IF EXISTS phisprod.rm_request_facility CASCADE;

CREATE TABLE rm_request_facility
(
   facility_req_seqno          bigint,
   facility_req_code           varchar(50),
   facility_req_name           varchar(100),
   facility_req_type           varchar(10),
   facility_req_type_desc      varchar(50),
   facility_req_group          varchar(10),
   facility_req_group_desc     varchar(50),
   ministry                    varchar(10),
   ministry_desc               varchar(50),
   facility_req_category       varchar(10),
   facility_req_category_desc  varchar(50),
   address1                    varchar(100),
   address2                    varchar(100),
   address3                    varchar(100),
   city                        varchar(100),
   postcode                    varchar(10),
   state                       varchar(20),
   state_desc                  varchar(100),
   country                     varchar(48),
   mobile_phone                varchar(20),
   email                       varchar(50),
   contact_person              varchar(250),
   contact_no                  varchar(250),
   parameter1                  varchar(100),
   parameter2                  varchar(100),
   parameter3                  numeric(8,4),
   parameter4                  numeric(8,4),
   parameter5                  date,
   created_date                timestamp      DEFAULT CURRENT_TIMESTAMP NOT NULL,
   updated_date                timestamp      DEFAULT CURRENT_TIMESTAMP NOT NULL,
   active_flag                 char(1)        DEFAULT 'A'::bpchar,
   created_by                  bigint         NOT NULL,
   updated_by                  bigint         NOT NULL
);

ALTER TABLE rm_request_facility
   ADD CONSTRAINT rm_request_facility_pkey
   PRIMARY KEY (facility_req_seqno);

--Create table supplier request (rm_request_supplier)

DROP TABLE IF EXISTS phisprod.rm_request_supplier CASCADE;

CREATE TABLE rm_request_supplier
(
   supplier_req_seqno      bigint,
   supplier_req_code       varchar(50),
   supplier_req_name       varchar(100),
   supplier_req_type       varchar(10),
   supplier_req_type_desc  varchar(50),
   company_reg_no          varchar(40),
   reg_expiry_date         timestamp,
   trs_reg_no              varchar(40),
   company_status          varchar(20),
   company_status_desc     varchar(100),
   address1                varchar(100),
   address2                varchar(100),
   address3                varchar(100),
   city                    varchar(30),
   state                   varchar(20),
   state_desc              varchar(100),
   postcode                varchar(5),
   country                 varchar(48),
   mobile_phone            varchar(20),
   email                   varchar(50),
   contact_person          varchar(30),
   contact_no              varchar(30),
   parameter1              varchar(100),
   parameter2              varchar(100),
   parameter3              numeric(8,4),
   parameter4              numeric(8,4),
   parameter5              date,
   created_date            timestamp      DEFAULT CURRENT_TIMESTAMP NOT NULL,
   updated_date            timestamp      DEFAULT CURRENT_TIMESTAMP NOT NULL,
   active_flag             char(1)        DEFAULT 'A'::bpchar,
   created_by              bigint         NOT NULL,
   updated_by              bigint         NOT NULL
);

ALTER TABLE rm_request_supplier
   ADD CONSTRAINT rm_request_supplier_pkey
   PRIMARY KEY (supplier_req_seqno);
