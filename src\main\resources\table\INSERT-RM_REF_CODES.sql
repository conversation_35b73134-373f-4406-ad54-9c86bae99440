
-- INSERT DOMAIN = 'ITEM_GROUP'
INSERT INTO rm_ref_codes (rc_seq,rc_domain,rc_value,rc_desc,active_flag,rc_remarks,created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (nextval('rm_ref_codes_seq'),'ITEM_GROUP','V','VACCINE','A',NULL,now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (rc_seq,rc_domain,rc_value,rc_desc,active_flag,rc_remarks,created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (nextval('rm_ref_codes_seq'),'ITEM_GROUP','R','REAGENT','A',NULL,now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (rc_seq,rc_domain,rc_value,rc_desc,active_flag,rc_remarks,created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (nextval('rm_ref_codes_seq'),'ITEM_GROUP','G','GAS','A',NULL,now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (rc_seq,rc_domain,rc_value,rc_desc,active_flag,rc_remarks,created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (nextval('rm_ref_codes_seq'),'ITEM_GROUP','X','X-RAY','A',NULL,now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);


-- INSERT DOMAIN = 'DATA_REQUEST_TYPE'
INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_REQUEST_TYPE',
  'N',
  'New',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (rc_seq,rc_domain,rc_value,rc_desc,active_flag,rc_remarks,created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (nextval('rm_ref_codes_seq'),'DATA_REQUEST_TYPE','U','Update','A',NULL,now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

-- INSERT DOMAIN = 'DATA_REQUEST_CATEGORY'
INSERT INTO rm_ref_codes (rc_seq,rc_domain,rc_value,rc_desc,active_flag,rc_remarks,created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (nextval('rm_ref_codes_seq'),'DATA_REQUEST_CATEGORY','IM','Item Master Maintenance','A',NULL,now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (rc_seq,rc_domain,rc_value,rc_desc,active_flag,rc_remarks,created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (nextval('rm_ref_codes_seq'),'DATA_REQUEST_CATEGORY','OM','Other Maintenance','A',NULL,now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);


-- INSERT DOMAIN = 'DATA_REQUEST_ASSIGNMENT'
INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_REQUEST_ASSIGNMENT',
  'CPFL',
  'Cawangan Pengurusan Farmasi Logistik',
  A,
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_REQUEST_ASSIGNMENT',
  'CPF',
  'Cawangan Pengurusan Formulari',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_REQUEST_ASSIGNMENT',
  'CPFS',
  'Cawangan Pengurusan Farmaseutikal',
  'A',
  "NULL",
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

-- INSERT DOMAIN = 'DATA_REQUEST_STATUS'
INSERT INTO rm_ref_codes (rc_seq,rc_domain,rc_value,rc_desc,active_flag,rc_remarks,created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (nextval('rm_ref_codes_seq'),'DATA_REQUEST_STATUS','P','Pending','A',"NULL",now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (rc_seq,rc_domain,rc_value,rc_desc,active_flag,rc_remarks,created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (nextval('rm_ref_codes_seq'),'DATA_REQUEST_STATUS','A','Approved','A',NULL,now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

-- INSERT DOMAIN = 'DATA_REQUEST_SUB_CATEGORY_NEW'
INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_REQUEST_SUB_CATEGORY_NEW',
  'IM',
  'Item Master',
  'A',
  'IM',
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_REQUEST_SUB_CATEGORY_NEW',
  'IP',
  'Item Packaging',
  'A',
  'IM',
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_REQUEST_SUB_CATEGORY_NEW',
  'FM',
  'Facility Master',
  'A',
  'OM',
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_REQUEST_SUB_CATEGORY_NEW',
  'SM',
  'Supplier Master',
  'A',
  'OM',
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_REQUEST_SUB_CATEGORY_NEW',
  'O',
  'Others',
  'A',
  'OM',
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);


-- INSERT DOMAIN = 'DATA_REQUEST_INTENTION'
INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_REQUEST_INTENTION',
  'PO',
  'Purchase Only',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_REQUEST_INTENTION',
  'PPD',
  'Purchase and Prescribe by Doctor',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_REQUEST_INTENTION',
  'PI',
  'Prepacking Item',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_REQUEST_INTENTION',
  'PI',
  'Prepacking Item',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_REQUEST_INTENTION',
  'MI',
  'Manufactured Item',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_REQUEST_INTENTION',
  'SAM',
  'Special Approval Medicine',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

-- INSERT DOMAIN = 'FACILITY_GROUP'
INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'FACILITY_GROUP',
  'PB',
  'Public',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'FACILITY_GROUP',
  'PV',
  'Private',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

-- INSERT DOMAIN = 'FACILITY_MINISTRY'
INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'FACILITY_MINISTRY',
  'MOH',
  'MOH',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'FACILITY_MINISTRY',
  'NMOH',
  'Non-MOH',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

  -- INSERT DOMAIN = 'DATA_ITEM_DETAILS'
INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_ITEM_DETAILS',
  'AR',
  'Administration Route',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_ITEM_DETAILS',
  'CI',
  'Cautionary & Instruction',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_ITEM_DETAILS',
  'F',
  'Frequency',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_ITEM_DETAILS',
  'I',
  'Indication',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_ITEM_DETAILS',
  'MC',
  'Manufacturing Configuration',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'DATA_ITEM_DETAILS',
  'O',
  'Others',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

-- INSERT DOMAIN = 'MOH_DIVISION'
INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'MOH_DIVISION',
  'CPFL',
  'Cawangan Pengurusan Farmasi Logistik',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'MOH_DIVISION',
  'CPF',
  'Cawangan Pengurusan Formulari',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'MOH_DIVISION',
  'CPFS',
  'Cawangan Penjagaan Farmaseutikal',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'MOH_DIVISION',
  'CPHU',
  'Cawangan Pengurusan Harga Ubat',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);

INSERT INTO rm_ref_codes (
  rc_seq,
  rc_domain,
  rc_value,
  rc_desc,
  active_flag,
  rc_remarks,
  created_date,updated_date,rc_smrp_value,parameter1,parameter2,parameter3,parameter4,parameter5,created_by,updated_by,iwp_sync_date,rc_smrp_v2_value) 
VALUES (
  nextval('rm_ref_codes_seq'),
  'MOH_DIVISION',
  'CTMIF',
  'Cawangan Teknologi Maklumat & Informatik Farmasi',
  'A',
  NULL,
  now(),now(),NULL,NULL,NULL,NULL,NULL,NULL,194,194,now(),NULL);
