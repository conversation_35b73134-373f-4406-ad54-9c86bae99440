package my.com.cmg.rms.security;

import java.util.Collection;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import my.com.cmg.rms.dto.security.AuthUserDTO;
import my.com.cmg.rms.service.IAuthorizationService;
import my.com.cmg.rms.service.IUserService;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

@AllArgsConstructor
public class CustomJwtConverter implements Converter<Jwt, AbstractAuthenticationToken> {

  private final IUserService userService;
  private final IAuthorizationService authorizationService;

  @Override
  public AbstractAuthenticationToken convert(Jwt jwt) {
    String username = jwt.getClaimAsString("preferred_username");

    AuthUserDTO authUser = userService.getAuthUserByUsername(username);
    if (authUser == null) {
      return new JwtAuthenticationToken(jwt);
    }

    Collection<GrantedAuthority> authorities =
        authUser.roles().stream()
            .map(role -> new SimpleGrantedAuthority("ROLE_" + role))
            .collect(Collectors.toList());

    return new JwtAuthenticationToken(jwt, authorities, username);
  }
}